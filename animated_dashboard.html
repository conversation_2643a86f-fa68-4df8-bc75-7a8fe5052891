<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Tech Dashboard</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a1a3a 0%, #1e3a8a 50%, #3b82f6 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .dashboard-container {
            position: relative;
            width: 800px;
            height: 600px;
            perspective: 1000px;
        }

        .laptop {
            position: absolute;
            top: 50px;
            left: 150px;
            width: 350px;
            height: 245px;
            background: linear-gradient(145deg, #1e293b, #334155);
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            transform: rotateX(10deg) rotateY(-5deg);
            transition: all 0.5s ease;
        }

        .laptop:hover {
            transform: rotateX(15deg) rotateY(-10deg) scale(1.05);
        }

        .laptop-screen {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 320px;
            height: 192px;
            background: linear-gradient(145deg, #ec4899, #8b5cf6);
            border-radius: 8px;
            padding: 10px;
            box-sizing: border-box;
        }

        .screen-content {
            width: 100%;
            height: 100%;
            background: #0f172a;
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .window-bar {
            height: 25px;
            background: linear-gradient(90deg, #10b981, #06b6d4);
            border-radius: 4px 4px 0 0;
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .window-buttons {
            display: flex;
            gap: 5px;
        }

        .window-button {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
            animation: pulse 2s ease-in-out infinite;
        }

        .content-area {
            display: flex;
            height: calc(100% - 25px);
            padding: 10px;
            gap: 10px;
        }

        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .blue-section {
            height: 60px;
            background: linear-gradient(45deg, #3b82f6, #06b6d4);
            border-radius: 4px;
            animation: glow 3s ease-in-out infinite alternate;
        }

        .green-section {
            height: 60px;
            background: linear-gradient(45deg, #10b981, #22c55e);
            border-radius: 4px;
            animation: glow 3s ease-in-out infinite alternate 1s;
        }

        .right-panel {
            flex: 2;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .data-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .line {
            height: 2px;
            background: linear-gradient(90deg, #10b981, #06b6d4);
            margin: 3px 0;
            border-radius: 1px;
            animation: pulse-line 2s ease-in-out infinite;
        }

        .line:nth-child(odd) {
            animation-delay: 0.5s;
        }

        .floating-windows {
            position: absolute;
        }

        .window-1 {
            top: 350px;
            left: 100px;
            width: 150px;
            height: 90px;
            background: linear-gradient(145deg, #ec4899, #f59e0b);
            border-radius: 8px;
            transform: rotateY(15deg) rotateX(-5deg);
            animation: float 4s ease-in-out infinite;
        }

        .window-2 {
            top: 350px;
            right: 100px;
            width: 150px;
            height: 90px;
            background: linear-gradient(145deg, #8b5cf6, #06b6d4);
            border-radius: 8px;
            transform: rotateY(-15deg) rotateX(5deg);
            animation: float 4s ease-in-out infinite 2s;
        }

        .chart-area {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background: rgba(15, 23, 42, 0.8);
            border-radius: 6px;
            display: flex;
            align-items: end;
            justify-content: center;
            gap: 5px;
            padding: 20px;
        }

        .bar {
            width: 15px;
            background: linear-gradient(to top, #10b981, #22c55e);
            border-radius: 2px;
            animation: bar-grow 3s ease-in-out infinite;
        }

        .bar:nth-child(1) { height: 30%; animation-delay: 0s; }
        .bar:nth-child(2) { height: 60%; animation-delay: 0.5s; }
        .bar:nth-child(3) { height: 45%; animation-delay: 1s; }
        .bar:nth-child(4) { height: 80%; animation-delay: 1.5s; }
        .bar:nth-child(5) { height: 35%; animation-delay: 2s; }

        .connection-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, #06b6d4, transparent);
            animation: flow 3s linear infinite;
        }

        .line-1 {
            top: 150px;
            left: 100px;
            width: 200px;
            transform: rotate(15deg);
        }

        .line-2 {
            top: 400px;
            right: 150px;
            width: 150px;
            transform: rotate(-25deg);
            animation-delay: 1s;
        }

        .dots {
            display: none;
        }

        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
            100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4); }
        }

        @keyframes pulse-line {
            0%, 100% { opacity: 0.4; transform: scaleX(0.8); }
            50% { opacity: 1; transform: scaleX(1); }
        }

        @keyframes float {
            0%, 100% { transform: rotateY(15deg) rotateX(-5deg) translateY(0px); }
            50% { transform: rotateY(15deg) rotateX(-5deg) translateY(-10px); }
        }

        @keyframes bar-grow {
            0%, 100% { transform: scaleY(0.8); opacity: 0.7; }
            50% { transform: scaleY(1.1); opacity: 1; }
        }

        @keyframes flow {
            0% { transform: translateX(-100%) rotate(15deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(200%) rotate(15deg); opacity: 0; }
        }

        /* Responsive adjustments */
        @media (max-width: 900px) {
            .dashboard-container {
                width: 90vw;
                height: 70vh;
                transform: scale(0.8);
            }
        }

        .rotating-images {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .rotating-image {
            position: absolute;
            width: 60px;
            height: 60px;
            object-fit: contain;
            transition: transform 0.3s ease;
            background: transparent;
            transform-origin: center;
        }

        .rotating-image:hover {
            transform: scale(1.2);
            z-index: 10;
            pointer-events: auto;
        }

        .image-orbit {
            position: absolute;
            width: 100%;
            height: 100%;
            transform: rotate(0deg);
        }

        .image-orbit.rotating {
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(var(--rotation-angle, 0deg)); }
            to { transform: rotate(calc(var(--rotation-angle, 0deg) + 360deg)); }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="rotating-images">
            <div class="image-orbit" style="width: 550px; height: 550px; top: -75px; left: 35px;">
                <!-- Top -->
                <img src="image/file_11634597.png" alt="Icon 1" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                <!-- Top Right -->
                <img src="image/document_11665380.png" alt="Icon 2" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                <!-- Bottom Right -->
                <img src="image/c-sharp_6132221.png" alt="Icon 3" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                <!-- Bottom -->
                <img src="image/web-coding_11513813.png" alt="Icon 4" class="rotating-image" style="bottom: -5%; left: 50%; transform: translate(-50%, 50%);">
                <!-- Bottom Left -->
                <img src="image/code_4997543.png" alt="Icon 5" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                <!-- Top Left -->
                <img src="image/java_3291669.png" alt="Icon 6" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
            </div>
        </div>
        <!-- Main Laptop -->
        <div class="laptop">
            <div class="laptop-screen">
                <div class="screen-content">
                    <div class="window-bar">
                        <div class="window-buttons">
                            <div class="window-button"></div>
                            <div class="window-button"></div>
                            <div class="window-button"></div>
                        </div>
                    </div>
                    <div class="content-area">
                        <div class="left-panel">
                            <div class="blue-section"></div>
                            <div class="green-section"></div>
                        </div>
                        <div class="right-panel">
                            <div class="data-lines">
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                                <div class="line"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

       

        <!-- Connection Lines -->
        <div class="connection-lines">
            <div class="connection-line line-1"></div>
            <div class="connection-line line-2"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');
            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            let lastTimestamp = 0;
            const rotationSpeed = 0.5;

            function animate(timestamp) {
                if (!lastTimestamp) lastTimestamp = timestamp;
                
                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    lastTimestamp = timestamp;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    lastTimestamp = 0;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
                imageOrbit.style.transition = 'none';
            });
        });
    </script>
</body>
</html>