<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCT - Advanced Technical Solutions</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>

    <div class="floating-particles" id="particles"></div>
    <div class="meteor" id="meteor-1"></div>
    <div class="meteor" id="meteor-2"></div>
    <div class="meteor" id="meteor-3"></div>

    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>

            </div>
            <div class="nav-center">
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#contact">Contact Us</a></li>
                </ul>
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- الصفحة الرئيسية -->
        <section id="home" class="section">
            <div class="hero-section">
                <div class="hero-logo fade-in-element">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="hero-content">
                    <h1 class="fade-in-element delay-1">MCT</h1>
                    <p class="fade-in-element delay-2">
                        We provide advanced technical solutions that meet the needs of the digital age. Our team of specialized experts works to provide network services, software, and information security with the highest standards of quality and professionalism.
                    </p>
                    <button class="cta-button fade-in-element delay-3">Discover Our Services</button>
                </div>
            </div>
        </section>

        <!-- قسم الخدمات -->
        <section id="services" class="section">
            <div class="services-section">
                <h2 class="section-title fade-in-element">Our Distinguished Services</h2>
                <div class="services-grid">
                    <a href="networking-en.html" class="service-card fade-in-element delay-1">
                        <div class="service-icon"><i class="fas fa-network-wired"></i></div>
                        <h3>Network Solutions</h3>
                        <p>Design, implementation and maintenance of local and wide networks with the latest technologies and the highest standards of security and performance.</p>
                    </a>
                    <a href="software-en.html" class="service-card fade-in-element delay-2">
                        <div class="service-icon"><i class="fas fa-code"></i></div>
                        <h3>Software Development</h3>
                        <p>Development of custom applications and software using the latest technologies to meet different business needs.</p>
                    </a>
                    <a href="cybersecurity-en.html" class="service-card fade-in-element delay-3">
                        <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
                        <h3>Information Security</h3>
                        <p>Protecting data and systems from cyber threats and implementing best practices for digital security.</p>
                    </a>
                    <a href="security-assessment-en.html" class="service-card fade-in-element delay-4">
                        <div class="service-icon"><i class="fas fa-search"></i></div>
                        <h3>Security Assessment</h3>
                        <p>Evaluation and examination of systems and networks to discover security vulnerabilities and provide necessary recommendations.</p>
                    </a>
                    <a href="maintenance-en.html" class="service-card fade-in-element delay-5">
                        <div class="service-icon"><i class="fas fa-tools"></i></div>
                        <h3>Support & Maintenance</h3>
                        <p>Regular and emergency maintenance services for technical systems and equipment to ensure business continuity.</p>
                    </a>

                </div>
            </div>
        </section>

        <!-- قسم التواصل -->
        <section id="contact" class="section">
            <div class="contact-section">
                <h2 class="section-title fade-in-element">Contact Us</h2>
                <div class="contact-container fade-in-element delay-1">
                    <div class="contact-grid">
                        <div class="contact-item">
                            <h4><i class="fas fa-envelope"></i> Email</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-phone"></i> Phone Numbers</h4>
                            <p><a href="tel:+966123456789">+966 12 345 6789</a></p>
                            <p><a href="tel:+966987654321">+966 98 765 4321</a></p>
                            <p><a href="tel:+966555123456">+966 55 512 3456</a></p>
                        </div>
                        <div class="contact-item">
                            <h4><i class="fas fa-map-marker-alt"></i> Address</h4>
                            <p>Riyadh, Saudi Arabia</p>
                            <p>King Fahd Street, Technology Tower</p>
                            <p>10th Floor, Office 1002</p>
                        </div>
                    </div>

                    <h4 class="social-media-heading">Follow us on social media</h4>
                    <div class="social-links">
                        <a href="#" class="social-link" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link" title="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link" title="YouTube"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="social-link" title="WhatsApp"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- تذييل الصفحة -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <img src="image/logo-8.png" alt="MCT Logo">
            </div>
            <p>We provide innovative technical solutions that meet the needs of the digital age</p>
            <div class="footer-links">
                <a href="#home">Home</a>
                <a href="#services">Services</a>
                <a href="#contact">Contact Us</a>
            </div>
        </div>
    </footer>

    <!-- زر الانتقال للأعلى -->
    <div class="scroll-top" id="scrollTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثير التمرير السلس
        function smoothScroll() {
            const navLinks = document.querySelectorAll('.nav-links a, .cta-button, .footer-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    if (link.getAttribute('href').startsWith('#')) {
                        e.preventDefault();
                        const targetId = link.getAttribute('href');
                        const targetSection = document.querySelector(targetId);
                        if (targetSection) {
                            targetSection.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }

        // تأثير شريط التنقل عند التمرير
        function navbarScrollEffect() {
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(15, 15, 35, 0.98)';
                    navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
                } else {
                    navbar.style.background = 'rgba(15, 15, 35, 0.95)';
                    navbar.style.boxShadow = 'none';
                }
            });
        }

        // تأثير الظهور التدريجي عند التمرير
        function scrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // مراقبة جميع العناصر التي تحتوي على كلاس fade-in-element
            const fadeElements = document.querySelectorAll('.fade-in-element');
            fadeElements.forEach(element => {
                observer.observe(element);
            });
        }

        // زر الانتقال للأعلى
        function setupScrollTop() {
            const scrollTopBtn = document.getElementById('scrollTop');

            window.addEventListener('scroll', () => {
                if (window.scrollY > 500) {
                    scrollTopBtn.classList.add('visible');
                } else {
                    scrollTopBtn.classList.remove('visible');
                }
            });

            scrollTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // تكرار حركة الشهاب
        function randomizeMeteor() {
            const meteors = document.querySelectorAll('.meteor');

            meteors.forEach(meteor => {
                meteor.style.top = Math.random() * 10 + '%';
                meteor.style.left = Math.random() * 10 + '%';
                meteor.style.animationDelay = Math.random() * 5 + 's';
            });
        }

        // تهيئة الموقع
        function initWebsite() {
            createParticles();
            smoothScroll();
            navbarScrollEffect();
            scrollAnimations();
            setupScrollTop();
            randomizeMeteor();
            enhanceServiceCards(); // إضافة تحسينات الخدمات
            setInterval(randomizeMeteor, 5000);
        }



        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                showPageTransition('Switching to Arabic...', 'جاري التبديل إلى العربية...', () => {
                    window.location.href = 'index.html';
                });
            }
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            // إنشاء عنصر الانتقال
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;

            document.body.appendChild(transition);

            // تفعيل الانتقال
            setTimeout(() => {
                transition.classList.add('active');
            }, 50);

            // تنفيذ الانتقال بعد التأثير
            setTimeout(() => {
                callback();
            }, 1200);
        }

        // تحسين تأثيرات hover للخدمات
        function enhanceServiceCards() {
            const serviceCards = document.querySelectorAll('.service-card');

            serviceCards.forEach(card => {
                // إضافة تأثير الماوس
                card.addEventListener('mouseenter', function() {
                    // إضافة تأثير صوتي بصري
                    this.style.transform = 'translateY(-10px) rotateX(5deg) scale(1.02)';

                    // تأثير الأيقونة
                    const icon = this.querySelector('.service-icon i');
                    if (icon) {
                        icon.style.animation = 'iconFloat 2s ease-in-out infinite';
                    }

                    // تأثير النص
                    const title = this.querySelector('h3');
                    const description = this.querySelector('p');

                    if (title) {
                        title.style.transform = 'translateX(5px)';
                        title.style.color = 'var(--accent-color)';
                    }

                    if (description) {
                        description.style.transform = 'translateX(3px)';
                        description.style.color = '#e0e0e0';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    // إعادة تعيين التأثيرات
                    this.style.transform = 'translateY(0) rotateX(0) scale(1)';

                    const icon = this.querySelector('.service-icon i');
                    if (icon) {
                        icon.style.animation = '';
                    }

                    const title = this.querySelector('h3');
                    const description = this.querySelector('p');

                    if (title) {
                        title.style.transform = 'translateX(0)';
                        title.style.color = '';
                    }

                    if (description) {
                        description.style.transform = 'translateX(0)';
                        description.style.color = '';
                    }
                });

                // تأثير النقر
                card.addEventListener('click', function(e) {
                    // تأثير الموجة عند النقر
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        border-radius: 50%;
                        background: rgba(0, 139, 128, 0.6);
                        transform: scale(0);
                        animation: ripple 0.6s linear;
                        pointer-events: none;
                    `;

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        }

        // تشغيل التهيئة عند تحميل الصفحة
        window.addEventListener('load', () => {
            initWebsite();
        });
    </script>
    <script src="js/translation.js"></script>
</body>
</html>
