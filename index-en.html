<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCT - Technical Services</title>
    <meta name="description" content="We provide integrated and innovative technical solutions">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Meteor Effects -->
    <div class="meteor" id="meteor-1"></div>
    <div class="meteor" id="meteor-2"></div>
    <div class="meteor" id="meteor-3"></div>

    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                    <li><a href="consulting-en.html">Cloud Solutions</a></li>
                </ul>
            </div>
            
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Our Technical Services</h1>
                <p class="hero-subtitle">We provide integrated and innovative technical solutions</p>
                <a href="#services" class="cta-button">Discover Our Services</a>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="services-section">
            <h2 class="section-title">Our Services</h2>
            <div class="services-grid">
                <a href="networking-en.html" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3>Network Solutions</h3>
                    <p>Integrated and reliable network solutions that ensure smooth and secure connectivity for all your systems.</p>
                </a>

                <a href="software-en.html" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Software Development</h3>
                    <p>Custom software development using the latest technologies and programming languages to meet your business needs.</p>
                </a>

                <a href="cybersecurity-en.html" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Information Security</h3>
                    <p>Comprehensive protection for your systems and data from cyber threats using the latest security technologies.</p>
                </a>

                <a href="security-assessment-en.html" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Security Assessment</h3>
                    <p>Comprehensive evaluation of your systems' security to identify vulnerabilities and provide improvement recommendations.</p>
                </a>

                <a href="maintenance-en.html" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Support & Maintenance</h3>
                    <p>Continuous technical support and regular maintenance to ensure optimal performance of your systems.</p>
                </a>


            </div>
        </section>

        <!-- About Section -->
        <section class="about-section">
            <h2 class="section-title">About Us</h2>
            <p class="about-text">
                We are a leading company in providing technical solutions, offering integrated services in networks, software development, 
                information security, and technical support. We strive to provide innovative and reliable solutions that meet our clients' needs 
                and help them achieve their business goals.
            </p>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <h2 class="section-title">Contact Us</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <h3>Phone</h3>
                    <p>+*********** 789</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <h3>Email</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Address</h3>
                    <p>Riyadh, Saudi Arabia</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <p>&copy; 2024 MCT. All rights reserved.</p>
    </footer>

    <script>
        // Particle System
        function createParticles() {
            const particleContainer = document.createElement('div');
            particleContainer.className = 'particles';
            document.body.appendChild(particleContainer);

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Meteor Animation
        function animateMeteors() {
            const meteors = document.querySelectorAll('.meteor');
            meteors.forEach((meteor, index) => {
                meteor.style.animationDelay = (index * 2) + 's';
            });
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'ar') {
                window.location.href = 'index.html';
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            createParticles();
            animateMeteors();
        });
    </script>
    <script src="js/scroll-animations.js"></script>
</body>
</html>
