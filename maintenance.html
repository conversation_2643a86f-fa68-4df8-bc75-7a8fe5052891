<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصيانة التقنية - MCT</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        :root { --primary-color: #28909A; --secondary-color: #1c5c62; --accent-color-1: #F7A600; --accent-color-2: #008B80; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e); color: #ffffff; overflow-x: hidden; position: relative; line-height: 1.6; }
        .animated-bg { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e); }
        .floating-particles { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; pointer-events: none; }
        .particle { position: absolute; width: 6px; height: 6px; background: rgb(0, 151, 139); border-radius: 50%; animation: float 8s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; } 50% { transform: translateY(-30px) rotate(180deg); opacity: 1; } }
        .header { background: rgba(15, 15, 35, 0.95); backdrop-filter: blur(20px); padding: 20px 5%; border-bottom: 1px solid rgba(255, 255, 255, 0.1); position: fixed; top: 0; width: 100%; z-index: 1000; }
        .header-content { display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; }
        .logo-section { display: flex; align-items: center; gap: 15px; }
        .logo { width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; overflow: hidden; }
        .logo img { width: 100%; height: 100%; object-fit: contain; }
        .company-name { font-size: 24px; font-weight: bold; color: #ffffff; }
        .back-btn { padding: 10px 20px; background: linear-gradient(135deg, #008B80, #1c5c62); color: white; border: none; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; }
        .back-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(0, 139, 128, 0.4); }
        .main-content { margin-top: 100px; padding: 50px 5%; max-width: 1200px; margin-left: auto; margin-right: auto; }
        .service-hero { text-align: center; margin-bottom: 80px; }
        .service-icon { width: 120px; height: 120px; background: linear-gradient(135deg, #008B80, #F7A600); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 30px; font-size: 50px; color: white; box-shadow: 0 20px 40px rgba(0, 139, 128, 0.3); animation: pulse 3s ease-in-out infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        .service-title { font-size: 3.5rem; margin-bottom: 20px; background: linear-gradient(135deg, #008B80, #F7A600); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .service-subtitle { font-size: 1.3rem; color: #b0b0b0; max-width: 800px; margin: 0 auto; }
        .content-section { margin-bottom: 60px; }
        .section-title { font-size: 2.2rem; color: #008B80; margin-bottom: 30px; text-align: center; position: relative; }
        .section-title::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 3px; background: linear-gradient(135deg, #008B80, #F7A600); border-radius: 2px; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 40px; }
        .feature-card { background: rgba(255, 255, 255, 0.08); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 30px; transition: all 0.3s ease; position: relative; overflow: hidden; }
        .feature-card::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1)); transition: left 0.5s ease; z-index: -1; }
        .feature-card:hover::before { left: 0; }
        .feature-card:hover { transform: translateY(-10px); border-color: rgba(0, 139, 128, 0.3); box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2); }
        .feature-card h3 { font-size: 1.4rem; color: #F7A600; margin-bottom: 15px; }
        .feature-card p { color: #c0c0c0; line-height: 1.7; }
        .services-list { list-style: none; padding: 0; }
        .services-list li { background: rgba(255, 255, 255, 0.05); margin-bottom: 15px; padding: 20px; border-radius: 15px; border-left: 4px solid #008B80; transition: all 0.3s ease; }
        .services-list li:hover { background: rgba(0, 139, 128, 0.1); transform: translateX(10px); }
        .contact-cta { text-align: center; margin-top: 60px; padding: 40px; background: rgba(255, 255, 255, 0.05); border-radius: 25px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .cta-button { padding: 15px 40px; background: linear-gradient(135deg, #008B80, #F7A600); color: white; border: none; border-radius: 30px; font-size: 1.1rem; font-weight: bold; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; margin: 10px; }
        .cta-button:hover { transform: translateY(-3px); box-shadow: 0 15px 40px rgba(0, 139, 128, 0.5); }
        @media (max-width: 768px) { .service-title { font-size: 2.5rem; } .features-grid { grid-template-columns: 1fr; } .main-content { padding: 30px 5%; } }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="company-name">MCT</div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button id="lang-toggle" style="background: linear-gradient(135deg, #008B80, #1c5c62); color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: 500; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="service-hero">
            <div class="service-icon">🔧</div>
            <h1 class="service-title">الصيانة التقنية</h1>
            <p class="service-subtitle">نقدم خدمات صيانة شاملة لضمان استمرارية عمل أنظمتكم التقنية بأعلى كفاءة. فريقنا المتخصص متاح على مدار الساعة لحل أي مشاكل تقنية.</p>
        </section>
        <section class="content-section">
            <h2 class="section-title">خدمات الصيانة التقنية</h2>
            <ul class="services-list">
                <li><strong>الصيانة الوقائية:</strong> فحص دوري للأنظمة لتجنب الأعطال قبل حدوثها</li>
                <li><strong>الصيانة التصحيحية:</strong> إصلاح سريع للأعطال والمشاكل التقنية</li>
                <li><strong>صيانة الشبكات:</strong> فحص وصيانة المعدات الشبكية والكابلات</li>
                <li><strong>صيانة الخوادم:</strong> مراقبة وصيانة الخوادم وقواعد البيانات</li>
                <li><strong>تحديث الأنظمة:</strong> تحديث البرمجيات وأنظمة التشغيل</li>
                <li><strong>النسخ الاحتياطي:</strong> إدارة وفحص أنظمة النسخ الاحتياطي</li>
                <li><strong>الدعم الطارئ:</strong> استجابة سريعة للحالات الطارئة على مدار الساعة</li>
            </ul>
        </section>
        <section class="content-section">
            <h2 class="section-title">مميزات خدماتنا</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>استجابة سريعة</h3>
                    <p>فريق دعم متاح على مدار الساعة للاستجابة السريعة لأي مشاكل تقنية</p>
                </div>
                <div class="feature-card">
                    <h3>خبرة متخصصة</h3>
                    <p>فنيون معتمدون ومتخصصون في جميع أنواع الأنظمة والمعدات التقنية</p>
                </div>
                <div class="feature-card">
                    <h3>أدوات متقدمة</h3>
                    <p>استخدام أحدث الأدوات والتقنيات في التشخيص والإصلاح</p>
                </div>
                <div class="feature-card">
                    <h3>صيانة وقائية</h3>
                    <p>برامج صيانة وقائية مخططة لتجنب الأعطال والمشاكل المستقبلية</p>
                </div>
                <div class="feature-card">
                    <h3>تقارير مفصلة</h3>
                    <p>تقارير شاملة عن حالة الأنظمة والأعمال المنجزة والتوصيات</p>
                </div>
                <div class="feature-card">
                    <h3>ضمان الجودة</h3>
                    <p>ضمان على جميع أعمال الصيانة والإصلاح مع متابعة دورية</p>
                </div>
            </div>
        </section>
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاجون لصيانة أنظمتكم التقنية؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصلوا معنا للحصول على خدمات صيانة احترافية تضمن استمرارية أعمالكم</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        window.addEventListener('load', createParticles);

        // نظام الترجمة مع حفظ اللغة
        document.getElementById('lang-toggle').addEventListener('click', function() {
            const btn = this;
            const isArabic = document.documentElement.lang === 'ar';
            const newLang = isArabic ? 'en' : 'ar';

            // حفظ اللغة
            localStorage.setItem('selectedLanguage', newLang);

            document.documentElement.lang = newLang;
            btn.textContent = isArabic ? 'AR' : 'EN';

            const translations = {
                'الدعم والصيانة': 'Support and Maintenance',
                'نقدم خدمات دعم وصيانة شاملة لضمان استمرارية عمل أنظمتكم التقنية بأعلى كفاءة. فريقنا المتخصص متاح على مدار الساعة لتقديم الدعم الفني والصيانة الوقائية والطارئة.': 'We provide comprehensive support and maintenance services to ensure the continuity of your technical systems with the highest efficiency. Our specialized team is available 24/7 to provide technical support, preventive and emergency maintenance.',
                'خدمات الدعم والصيانة': 'Support and Maintenance Services',
                'الصيانة الوقائية: جدولة صيانة دورية لمنع الأعطال والمشاكل التقنية': 'Preventive Maintenance: Scheduling periodic maintenance to prevent failures and technical problems',
                'الدعم الفني: دعم تقني متخصص على مدار الساعة لحل المشاكل الطارئة': 'Technical Support: Specialized technical support 24/7 to solve emergency problems',
                'صيانة الأجهزة: صيانة وإصلاح جميع أنواع الأجهزة والمعدات التقنية': 'Hardware Maintenance: Maintenance and repair of all types of technical devices and equipment',
                'صيانة البرمجيات: تحديث وصيانة الأنظمة والتطبيقات البرمجية': 'Software Maintenance: Updating and maintaining software systems and applications',
                'مراقبة الأنظمة: مراقبة مستمرة لأداء الأنظمة واكتشاف المشاكل مبكراً': 'System Monitoring: Continuous monitoring of system performance and early problem detection',
                'النسخ الاحتياطي: إعداد وإدارة أنظمة النسخ الاحتياطي لحماية البيانات': 'Backup Systems: Setting up and managing backup systems to protect data',
                'التحديثات الأمنية: تطبيق التحديثات الأمنية والحماية من التهديدات': 'Security Updates: Applying security updates and protection from threats',
                'لماذا تختار خدماتنا؟': 'Why Choose Our Services?',
                'استجابة سريعة': 'Quick Response',
                'فريق دعم متاح على مدار الساعة للاستجابة السريعة لأي مشاكل طارئة': '24/7 support team available for quick response to any emergency problems',
                'خبرة واسعة': 'Extensive Experience',
                'فنيون متخصصون مع خبرة واسعة في جميع أنواع الأنظمة والتقنيات': 'Specialized technicians with extensive experience in all types of systems and technologies',
                'صيانة شاملة': 'Comprehensive Maintenance',
                'خدمات صيانة شاملة تغطي جميع جوانب الأنظمة التقنية': 'Comprehensive maintenance services covering all aspects of technical systems',
                'توفير التكاليف': 'Cost Savings',
                'خطط صيانة مدروسة تساعد في توفير التكاليف وتجنب الأعطال المكلفة': 'Well-planned maintenance plans that help save costs and avoid expensive failures',
                'ضمان الجودة': 'Quality Assurance',
                'ضمان جودة الخدمة والالتزام بأعلى معايير الصيانة والدعم': 'Service quality guarantee and commitment to the highest maintenance and support standards',
                'تقارير دورية': 'Periodic Reports',
                'تقارير دورية مفصلة عن حالة الأنظمة والصيانة المنجزة': 'Detailed periodic reports on system status and completed maintenance',
                'هل تحتاج لدعم وصيانة موثوقة؟': 'Do You Need Reliable Support and Maintenance?',
                'تواصل معنا اليوم للحصول على خطة صيانة مخصصة تضمن استمرارية أعمالكم': 'Contact us today to get a customized maintenance plan that ensures business continuity',
                'تواصل معنا': 'Contact Us',
                'اتصل بنا': 'Call Us',
                'العودة للرئيسية': 'Back to Home'
            };

            document.querySelectorAll('h1, h2, h3, h4, p, button, a, li').forEach(el => {
                const original = el.textContent.trim();

                if (el.id === 'lang-toggle' || original === 'MCT') {
                    return;
                }

                if (isArabic) {
                    if (translations[original]) {
                        el.textContent = translations[original];
                    }
                } else {
                    for (const [ar, en] of Object.entries(translations)) {
                        if (en === original) {
                            el.textContent = ar;
                            break;
                        }
                    }
                }
            });
        });

        // تحميل اللغة المحفوظة عند تحميل الصفحة
        window.addEventListener('load', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            if (savedLang !== document.documentElement.lang) {
                document.getElementById('lang-toggle').click();
            }
        });
    </script>
</body>
</html>
