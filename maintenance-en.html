<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support & Maintenance - MCT</title>
    <meta name="description" content="Continuous technical support and regular maintenance to ensure optimal performance">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html">Security Assessment</a></li>
                    <li><a href="maintenance-en.html" class="active">Support & Maintenance</a></li>
                    <li><a href="consulting-en.html">Cloud Solutions</a></li>
                </ul>
            </div>
            
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-tools"></i></div>
            <h1 class="service-title">Support & Maintenance</h1>
            <p class="service-subtitle">
                We provide continuous technical support and regular maintenance to ensure optimal performance of your systems. 
                Our dedicated team is available 24/7 to resolve any issues and keep your operations running smoothly.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section">
            <h2 class="section-title">Our Support & Maintenance Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>24/7 Technical Support:</strong> Round-the-clock technical support to resolve any issues quickly</li>
                        <li><strong>Preventive Maintenance:</strong> Regular maintenance to prevent problems before they occur</li>
                        <li><strong>System Updates:</strong> Regular updates to keep your systems secure and up-to-date</li>
                        <li><strong>Performance Monitoring:</strong> Continuous monitoring of system performance and optimization</li>
                        <li><strong>Backup and Recovery:</strong> Data backup services and disaster recovery planning</li>
                        <li><strong>Remote Support:</strong> Quick remote assistance to resolve issues efficiently</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="Support File" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Support Document" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="Support Code" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Support Web" class="rotating-image" style="bottom: -5%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Support Programming" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Support Java" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="content-section">
            <h2 class="section-title">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>24/7 Availability</h3>
                    <p>Our support team is available around the clock to assist you whenever you need help</p>
                </div>
                <div class="feature-card">
                    <h3>Quick Response</h3>
                    <p>Fast response times to minimize downtime and keep your business running smoothly</p>
                </div>
                <div class="feature-card">
                    <h3>Expert Team</h3>
                    <p>Experienced technicians with expertise in various technologies and systems</p>
                </div>
                <div class="feature-card">
                    <h3>Proactive Approach</h3>
                    <p>Preventive maintenance to identify and resolve issues before they impact your operations</p>
                </div>
                <div class="feature-card">
                    <h3>Cost-Effective</h3>
                    <p>Affordable maintenance plans that provide excellent value for your investment</p>
                </div>
                <div class="feature-card">
                    <h3>Comprehensive Coverage</h3>
                    <p>Complete support for all your systems, software, and infrastructure needs</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need Reliable Technical Support?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today to learn about our comprehensive support and maintenance plans</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // Particle System
        function createParticles() {
            const particleContainer = document.createElement('div');
            particleContainer.className = 'particles';
            document.body.appendChild(particleContainer);

            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'ar') {
                window.location.href = 'maintenance.html';
            }
        }

        window.addEventListener('load', createParticles);
    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
</body>
</html>
