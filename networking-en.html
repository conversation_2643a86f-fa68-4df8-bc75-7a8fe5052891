<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Solutions - MCT</title>
    <meta name="description" content="Integrated and reliable network solutions that ensure smooth and secure connectivity">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html" class="active">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </header>

    <!-- Background Video -->
    <div class="video-background">
        <video id="bgVideo" autoplay muted loop>
            <source src="video/7021928_City_Smart_3840x2160%20.mp4" type="video/mp4">
            <source src="video/7021928_City_Smart_3840x2160 .mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-network-wired"></i></div>
            <h1 class="service-title">Network Solutions</h1>
            <p class="service-subtitle">
                We provide integrated and reliable network solutions that ensure smooth and secure connectivity for all your systems. 
                From design and implementation to maintenance and support, we are your trusted partner in the networking world.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section">
            <h2 class="section-title">Our Network Services</h2>
            <div class="services-with-video">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>Network Design:</strong> Custom network design that suits your needs and business size</li>
                        <li><strong>Implementation and Installation:</strong> Professional network implementation using the latest equipment and technologies</li>
                        <li><strong>Wireless Networks:</strong> Advanced and secure Wi-Fi solutions for all environments</li>
                        <li><strong>Server Networks:</strong> Setup and management of high-performance server networks</li>
                        <li><strong>Regular Maintenance:</strong> Comprehensive maintenance services to ensure network continuity</li>
                        <li><strong>Network Monitoring:</strong> Continuous performance monitoring and early problem detection</li>
                        <li><strong>Network Upgrades:</strong> Updating and upgrading existing networks to improve performance</li>
                    </ul>
                </div>
                <div class="services-video">
                    <video id="servicesVideo" muted loop preload="auto">
                        <source src="video/7021928_City_Smart_3840x2160%20.mp4" type="video/mp4">
                        <source src="video/7021928_City_Smart_3840x2160 .mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="content-section">
            <h2 class="section-title">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>High Performance</h3>
                    <p>High-speed and efficient networks that ensure optimal performance for all your applications and systems</p>
                </div>
                <div class="feature-card">
                    <h3>Complete Reliability</h3>
                    <p>Reliable network solutions with quality guarantees and backup plans for protection against failures</p>
                </div>
                <div class="feature-card">
                    <h3>Advanced Security</h3>
                    <p>Implementation of the highest security standards to protect your network from threats and unauthorized access</p>
                </div>
                <div class="feature-card">
                    <h3>Scalability</h3>
                    <p>Flexible networks that can be expanded and developed with your business growth and future requirements</p>
                </div>
                <div class="feature-card">
                    <h3>Excellent Technical Support</h3>
                    <p>Specialized technical support team available 24/7 to solve any problems or inquiries</p>
                </div>
                <div class="feature-card">
                    <h3>Optimal Cost</h3>
                    <p>Cost-effective solutions that achieve the best return on investment for your projects</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need a Reliable and High-Performance Network?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free consultation and comprehensive assessment of your networking needs</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثيرات الفيديو التفاعلية المحسنة
        function setupVideoInteraction() {
            const video = document.getElementById('bgVideo');

            if (!video) {
                console.log('Background video element not found');
                return;
            }

            console.log('Setting up background video interaction...');

            let isMouseInside = true;
            let mouseTimeout;

            // إعدادات الفيديو
            video.muted = true;
            video.loop = true;
            video.autoplay = true;
            video.playsInline = true;

            // محاولة تشغيل الفيديو فور<|im_start|>
            const playVideo = () => {
                video.play().then(() => {
                    console.log('Background video started playing');
                }).catch(e => {
                    console.log('Background video play failed:', e);
                    // المحاولة مرة أخرى بعد ثانية
                    setTimeout(() => {
                        video.play().catch(err => console.log('Background video second attempt failed:', err));
                    }, 1000);
                });
            };

            // تشغيل الفيديو عند التحميل
            video.addEventListener('loadeddata', playVideo);
            video.addEventListener('canplay', playVideo);

            // التشغيل فور<|im_start|> إذا كان الفيديو جاهز
            if (video.readyState >= 3) {
                playVideo();
            }

            // تشغيل الفيديو عند دخول الماوس للصفحة
            document.addEventListener('mouseenter', () => {
                isMouseInside = true;
                if (video.paused) {
                    playVideo();
                }
            });

            // إيقاف الفيديو عند خروج الماوس من الصفحة
            document.addEventListener('mouseleave', () => {
                isMouseInside = false;
                video.pause();
                video.style.transform = 'scale(1) translate(0px, 0px)';
            });

            // تحريك الفيديو مع الماوس
            document.addEventListener('mousemove', (e) => {
                if (!isMouseInside) return;

                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // حركة أكثر سلاسة ووضوح
                const moveX = (mouseX - 0.5) * 20;
                const moveY = (mouseY - 0.5) * 20;

                video.style.transform = `scale(1.05) translate(${moveX}px, ${moveY}px)`;

                // استئناف الفيديو إذا كان متوقف
                clearTimeout(mouseTimeout);
                if (video.paused && isMouseInside) {
                    playVideo();
                }

                // إيقاف الفيديو بعد 3 ثوان من عدم الحركة
                mouseTimeout = setTimeout(() => {
                    if (isMouseInside) {
                        video.pause();
                    }
                }, 3000);
            });

            // تشغيل الفيديو عند النقر على الصفحة
            document.addEventListener('click', playVideo, { once: true });

            // تشغيل الفيديو عند اللمس (للأجهزة المحمولة)
            document.addEventListener('touchstart', playVideo, { once: true });
        }

        // إعداد فيديو الخدمات
        function setupServicesVideo() {
            const servicesVideo = document.getElementById('servicesVideo');
            const videoContainer = document.querySelector('.services-video');

            if (!servicesVideo || !videoContainer) {
                console.log('Services video element not found');
                return;
            }

            console.log('Setting up services video...');

            // إعدادات الفيديو
            servicesVideo.muted = true;
            servicesVideo.loop = true;
            servicesVideo.playsInline = true;

            // تشغيل الفيديو عند دخول الماوس
            videoContainer.addEventListener('mouseenter', () => {
                servicesVideo.play().then(() => {
                    console.log('Services video resumed playing from:', servicesVideo.currentTime.toFixed(2) + 's');
                }).catch(e => {
                    console.log('Services video play failed:', e);
                });
            });

            // إيقاف الفيديو عند خروج الماوس (الاحتفاظ بالموضع)
            videoContainer.addEventListener('mouseleave', () => {
                servicesVideo.pause();
                console.log('Services video paused at:', servicesVideo.currentTime.toFixed(2) + 's');
                // لا نعيد تعيين currentTime حتى يكمل من نفس النقطة
            });

            // تشغيل/إيقاف الفيديو عند النقر
            videoContainer.addEventListener('click', () => {
                if (servicesVideo.paused) {
                    servicesVideo.play().then(() => {
                        console.log('Services video manually started from:', servicesVideo.currentTime.toFixed(2) + 's');
                    }).catch(e => {
                        console.log('Services video click play failed:', e);
                    });
                } else {
                    servicesVideo.pause();
                    console.log('Services video manually paused at:', servicesVideo.currentTime.toFixed(2) + 's');
                }
            });

            // التأكد من أن الفيديو يبدأ من البداية في أول مرة فقط
            servicesVideo.addEventListener('loadeddata', () => {
                if (servicesVideo.currentTime === 0) {
                    console.log('Services video loaded and ready');
                }
            });
        }

        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                showPageTransition('Switching to Arabic...', 'جاري التبديل إلى العربية...', () => {
                    window.location.href = 'networking.html';
                });
            }
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;
            document.body.appendChild(transition);
            setTimeout(() => transition.classList.add('active'), 50);
            setTimeout(() => callback(), 1200);
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            createParticles();
            setupVideoInteraction();
            setupServicesVideo();
        });


    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
