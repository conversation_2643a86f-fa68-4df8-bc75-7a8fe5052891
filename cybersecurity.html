<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمن المعلومات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- خلفية متحركة -->
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Information Security</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html" class="active">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
            <h1 class="service-title">أمن المعلومات</h1>
            <p class="service-subtitle">
                نحن نقدم حلول أمن المعلومات الشاملة لحماية بياناتكم وأنظمتكم من التهديدات الإلكترونية المتطورة. 
                فريقنا من خبراء الأمن السيبراني يعمل على تطبيق أفضل الممارسات العالمية لضمان أقصى درجات الحماية.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-2">
            <h2 class="section-title">خدماتنا في أمن المعلومات</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>تقييم المخاطر الأمنية:</strong> تحليل شامل للثغرات والمخاطر المحتملة في أنظمتكم</li>
                        <li><strong>تطبيق سياسات الأمان:</strong> وضع وتنفيذ سياسات أمنية متقدمة ومخصصة</li>
                        <li><strong>مراقبة الأنظمة:</strong> مراقبة مستمرة للأنظمة والشبكات لاكتشاف التهديدات</li>
                        <li><strong>التدريب الأمني:</strong> تدريب الموظفين على أفضل ممارسات الأمان الرقمي</li>
                        <li><strong>استجابة للحوادث:</strong> خطط سريعة للاستجابة والتعافي من الحوادث الأمنية</li>
                        <li><strong>التشفير وحماية البيانات:</strong> تطبيق تقنيات التشفير المتقدمة لحماية البيانات الحساسة</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="Security File" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Security Document" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="Security Code" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Security Web" class="rotating-image" style="bottom: -3%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Security Programming" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Security Java" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- المميزات -->
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>خبرة متقدمة</h3>
                    <p>فريق من الخبراء المعتمدين في أمن المعلومات مع سنوات من الخبرة في مواجهة التهديدات المتطورة</p>
                </div>
                <div class="feature-card">
                    <h3>تقنيات حديثة</h3>
                    <p>استخدام أحدث التقنيات والأدوات في مجال الأمن السيبراني لضمان أقصى درجات الحماية</p>
                </div>
                <div class="feature-card">
                    <h3>دعم مستمر</h3>
                    <p>دعم فني متواصل على مدار الساعة لضمان استمرارية الحماية ومواجهة أي تهديدات طارئة</p>
                </div>
                <div class="feature-card">
                    <h3>حلول مخصصة</h3>
                    <p>تصميم حلول أمنية مخصصة تتناسب مع طبيعة عملكم ومتطلباتكم الخاصة</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لحماية أنظمتكم؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم أمني شامل لأنظمتكم</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الصفحة
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                showPageTransition('Switching to English...', 'جاري التبديل إلى الإنجليزية...', () => {
                    sessionStorage.setItem('pageTransition', 'true');
                    window.location.href = 'cybersecurity-en.html';
                });
            }
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;
            document.body.appendChild(transition);
            setTimeout(() => transition.classList.add('active'), 50);
            setTimeout(() => callback(), 1200);
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
            } else {
                setTimeout(() => pageLoader.classList.add('fade-out'), 1500);
            }
            setTimeout(() => pageLoader.style.display = 'none', 2800);
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            const rotationSpeed = 0.5;

            function animate() {
                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
            });
        });

    </script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
