<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمن المعلومات - MCT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #28909A;
            --secondary-color: #1c5c62;
            --accent-color-1: #F7A600;
            --accent-color-2: #008B80;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
            line-height: 1.6;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
        }

        /* نقاط متحركة */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgb(0, 151, 139);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-30px) rotate(180deg); opacity: 1; }
        }

        /* Header */
        .header {
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(20px);
            padding: 20px 5%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
        }

        .back-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #008B80, #1c5c62);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 139, 128, 0.4);
        }

        /* Main Content */
        .main-content {
            margin-top: 100px;
            padding: 50px 5%;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .service-hero {
            text-align: center;
            margin-bottom: 80px;
        }

        .service-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 50px;
            color: white;
            box-shadow: 0 20px 40px rgba(0, 139, 128, 0.3);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .service-title {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-subtitle {
            font-size: 1.3rem;
            color: #b0b0b0;
            max-width: 800px;
            margin: 0 auto;
        }

        .content-section {
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 2.2rem;
            color: #008B80;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1));
            transition: left 0.5s ease;
            z-index: -1;
        }

        .feature-card:hover::before {
            left: 0;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 139, 128, 0.3);
            box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2);
        }

        .feature-card h3 {
            font-size: 1.4rem;
            color: #F7A600;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #c0c0c0;
            line-height: 1.7;
        }

        .services-list {
            list-style: none;
            padding: 0;
        }

        .services-list li {
            background: rgba(255, 255, 255, 0.05);
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #008B80;
            transition: all 0.3s ease;
        }

        .services-list li:hover {
            background: rgba(0, 139, 128, 0.1);
            transform: translateX(10px);
        }

        .contact-cta {
            text-align: center;
            margin-top: 60px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .cta-button {
            padding: 15px 40px;
            background: linear-gradient(135deg, #008B80, #F7A600);
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 139, 128, 0.5);
        }

        @media (max-width: 768px) {
            .service-title {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 30px 5%;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="company-name">MCT</div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button id="lang-toggle" style="
                    background: linear-gradient(135deg, #008B80, #1c5c62);
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: 500;
                    font-size: 0.9rem;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero">
            <div class="service-icon">🛡️</div>
            <h1 class="service-title">أمن المعلومات</h1>
            <p class="service-subtitle">
                نحن نقدم حلول أمن المعلومات الشاملة لحماية بياناتكم وأنظمتكم من التهديدات الإلكترونية المتطورة. 
                فريقنا من خبراء الأمن السيبراني يعمل على تطبيق أفضل الممارسات العالمية لضمان أقصى درجات الحماية.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section">
            <h2 class="section-title">خدماتنا في أمن المعلومات</h2>
            <ul class="services-list">
                <li><strong>تقييم المخاطر الأمنية:</strong> تحليل شامل للثغرات والمخاطر المحتملة في أنظمتكم</li>
                <li><strong>تطبيق سياسات الأمان:</strong> وضع وتنفيذ سياسات أمنية متقدمة ومخصصة</li>
                <li><strong>مراقبة الأنظمة:</strong> مراقبة مستمرة للأنظمة والشبكات لاكتشاف التهديدات</li>
                <li><strong>التدريب الأمني:</strong> تدريب الموظفين على أفضل ممارسات الأمان الرقمي</li>
                <li><strong>استجابة للحوادث:</strong> خطط سريعة للاستجابة والتعافي من الحوادث الأمنية</li>
                <li><strong>التشفير وحماية البيانات:</strong> تطبيق تقنيات التشفير المتقدمة لحماية البيانات الحساسة</li>
            </ul>
        </section>

        <!-- المميزات -->
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>خبرة متقدمة</h3>
                    <p>فريق من الخبراء المعتمدين في أمن المعلومات مع سنوات من الخبرة في مواجهة التهديدات المتطورة</p>
                </div>
                <div class="feature-card">
                    <h3>تقنيات حديثة</h3>
                    <p>استخدام أحدث التقنيات والأدوات في مجال الأمن السيبراني لضمان أقصى درجات الحماية</p>
                </div>
                <div class="feature-card">
                    <h3>دعم مستمر</h3>
                    <p>دعم فني متواصل على مدار الساعة لضمان استمرارية الحماية ومواجهة أي تهديدات طارئة</p>
                </div>
                <div class="feature-card">
                    <h3>حلول مخصصة</h3>
                    <p>تصميم حلول أمنية مخصصة تتناسب مع طبيعة عملكم ومتطلباتكم الخاصة</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لحماية أنظمتكم؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم أمني شامل لأنظمتكم</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الصفحة
        window.addEventListener('load', createParticles);

        // نظام الترجمة مع حفظ اللغة
        document.getElementById('lang-toggle').addEventListener('click', function() {
            const btn = this;
            const isArabic = document.documentElement.lang === 'ar';
            const newLang = isArabic ? 'en' : 'ar';

            // حفظ اللغة
            localStorage.setItem('selectedLanguage', newLang);

            document.documentElement.lang = newLang;
            btn.textContent = isArabic ? 'AR' : 'EN';

            const translations = {
                'أمن المعلومات': 'Cybersecurity',
                'نحن نقدم حلول أمن المعلومات الشاملة لحماية بياناتكم وأنظمتكم من التهديدات الإلكترونية المتطورة. فريقنا من خبراء الأمن السيبراني يعمل على تطبيق أفضل الممارسات العالمية لضمان أقصى درجات الحماية.': 'We provide comprehensive cybersecurity solutions to protect your data and systems from advanced electronic threats. Our cybersecurity experts team works to implement global best practices to ensure maximum protection.',
                'خدمات الفحص الأمني': 'Security Assessment Services',
                'تقييم المخاطر الأمنية: تحليل شامل للثغرات والمخاطر المحتملة في أنظمتكم': 'Security Risk Assessment: Comprehensive analysis of vulnerabilities and potential risks in your systems',
                'تطبيق سياسات الأمان: وضع وتنفيذ سياسات أمنية متقدمة ومخصصة': 'Security Policy Implementation: Development and implementation of advanced and customized security policies',
                'مراقبة الأنظمة: مراقبة مستمرة للأنظمة والشبكات لاكتشاف التهديدات': 'System Monitoring: Continuous monitoring of systems and networks to detect threats',
                'التدريب الأمني: تدريب الموظفين على أفضل ممارسات الأمان الرقمي': 'Security Training: Training employees on digital security best practices',
                'استجابة للحوادث: خطط سريعة للاستجابة والتعافي من الحوادث الأمنية': 'Incident Response: Quick plans for responding to and recovering from security incidents',
                'التشفير وحماية البيانات: تطبيق تقنيات التشفير المتقدمة لحماية البيانات الحساسة': 'Encryption and Data Protection: Implementation of advanced encryption techniques to protect sensitive data',
                'لماذا تختار خدماتنا؟': 'Why Choose Our Services?',
                'خبرة متقدمة': 'Advanced Expertise',
                'فريق من الخبراء المعتمدين في أمن المعلومات مع سنوات من الخبرة في مواجهة التهديدات المتطورة': 'Team of certified cybersecurity experts with years of experience in facing advanced threats',
                'تقنيات حديثة': 'Modern Technologies',
                'استخدام أحدث التقنيات والأدوات في مجال الأمن السيبراني لضمان أقصى درجات الحماية': 'Using the latest technologies and tools in cybersecurity to ensure maximum protection',
                'دعم مستمر': 'Continuous Support',
                'دعم فني متواصل على مدار الساعة لضمان استمرارية الحماية ومواجهة أي تهديدات طارئة': '24/7 technical support to ensure continuous protection and face any emergency threats',
                'حلول مخصصة': 'Customized Solutions',
                'تصميم حلول أمنية مخصصة تتناسب مع طبيعة عملكم ومتطلباتكم الخاصة': 'Designing customized security solutions that fit your business nature and specific requirements',
                'هل تحتاج لحماية أنظمتكم؟': 'Do You Need to Protect Your Systems?',
                'تواصل معنا اليوم للحصول على استشارة مجانية وتقييم أمني شامل لأنظمتكم': 'Contact us today for a free consultation and comprehensive security assessment of your systems',
                'تواصل معنا': 'Contact Us',
                'اتصل بنا': 'Call Us',
                'العودة للرئيسية': 'Back to Home'
            };

            document.querySelectorAll('h1, h2, h3, h4, p, button, a, li').forEach(el => {
                const original = el.textContent.trim();

                if (el.id === 'lang-toggle' || original === 'MCT') {
                    return;
                }

                if (isArabic) {
                    if (translations[original]) {
                        el.textContent = translations[original];
                    }
                } else {
                    for (const [ar, en] of Object.entries(translations)) {
                        if (en === original) {
                            el.textContent = ar;
                            break;
                        }
                    }
                }
            });
        });

        // تحميل اللغة المحفوظة عند تحميل الصفحة
        window.addEventListener('load', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            if (savedLang !== document.documentElement.lang) {
                document.getElementById('lang-toggle').click();
            }
        });
    </script>
</body>
</html>
