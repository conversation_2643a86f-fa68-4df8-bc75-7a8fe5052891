<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمن المعلومات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- خلفية متحركة -->
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Information Security</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html" class="active">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
            <h1 class="service-title fade-in-element delay-1">أمن المعلومات</h1>
            <p class="service-subtitle fade-in-element delay-2">
                نقدم حلول أمنية متكاملة لحماية بياناتكم وأنظمتكم من التهديدات الإلكترونية. فريقنا من خبراء الأمن السيبراني يضمن حماية شاملة لبنيتكم التحتية الرقمية.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">خدماتنا في أمن المعلومات</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list fade-in-element delay-1">
                        <li><strong>تقييم المخاطر الأمنية:</strong> تحليل شامل للمخاطر وتحديد نقاط الضعف في الأنظمة</li>
                        <li><strong>حماية البنية التحتية:</strong> تأمين الشبكات والخوادم والأنظمة من الهجمات الإلكترونية</li>
                        <li><strong>حماية البيانات:</strong> تشفير وحماية البيانات الحساسة من الوصول غير المصرح</li>
                        <li><strong>مراقبة الأمن:</strong> مراقبة مستمرة للأنظمة واكتشاف التهديدات مبكراً</li>
                        <li><strong>استجابة الحوادث:</strong> معالجة سريعة وفعالة للحوادث الأمنية</li>
                        <li><strong>تدريب الأمن:</strong> برامج تدريبية لرفع الوعي الأمني لدى الموظفين</li>
                        <li><strong>التوافق مع المعايير:</strong> ضمان توافق الأنظمة مع معايير الأمن العالمية</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="Security File" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Security Document" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="Security Code" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Security Web" class="rotating-image" style="bottom: -3%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Security Programming" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Security Java" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- المميزات -->
        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>خبرة واسعة</h3>
                    <p>فريق من خبراء الأمن السيبراني ذوي خبرة في مختلف مجالات الأمن الرقمي</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>حلول متكاملة</h3>
                    <p>حلول أمنية شاملة تغطي جميع جوانب البنية التحتية الرقمية</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>تقنيات متطورة</h3>
                    <p>استخدام أحدث التقنيات والأدوات في مجال الأمن السيبراني</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>استجابة سريعة</h3>
                    <p>فريق دعم متاح على مدار الساعة للتعامل مع الحوادث الأمنية</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>حماية مستمرة</h3>
                    <p>مراقبة وتحديث مستمر لضمان أقصى درجات الحماية</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>تكلفة منافسة</h3>
                    <p>حلول أمنية فعالة بأسعار تنافسية تناسب مختلف الميزانيات</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج إلى حماية متكاملة لبياناتكم وأنظمتكم؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة أمنية مجانية وتقييم شامل لاحتياجاتكم</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تهيئة الصفحة
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'cybersecurity-en.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            const rotationSpeed = 0.5;

            function animate() {
                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
            });
        });

    </script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
