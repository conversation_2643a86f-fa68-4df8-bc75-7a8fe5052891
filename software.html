<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطوير البرمجيات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Software Development</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html" class="active">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-code"></i></div>
            <h1 class="service-title">تطوير البرمجيات</h1>
            <p class="service-subtitle">نطور حلول برمجية مبتكرة ومخصصة تلبي احتياجاتكم الفريدة. من التطبيقات البسيطة إلى الأنظمة المعقدة، نحن شريككم في التحول الرقمي.</p>
        </section>
        <section class="content-section fade-in-element delay-2">
            <h2 class="section-title">خدماتنا في البرمجيات</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>تطوير التطبيقات المخصصة:</strong> برمجة تطبيقات مصممة خصيصاً لمتطلباتكم</li>
                        <li><strong>تطبيقات الويب:</strong> مواقع وتطبيقات ويب تفاعلية وسريعة الاستجابة</li>
                        <li><strong>تطبيقات الهاتف المحمول:</strong> تطبيقات iOS و Android احترافية</li>
                        <li><strong>أنظمة إدارة قواعد البيانات:</strong> حلول قواعد بيانات متقدمة وآمنة</li>
                        <li><strong>أنظمة إدارة المحتوى:</strong> منصات CMS مخصصة وسهلة الاستخدام</li>
                        <li><strong>التكامل مع الأنظمة:</strong> ربط الأنظمة المختلفة وتبادل البيانات</li>
                        <li><strong>الصيانة والدعم:</strong> دعم مستمر وتحديثات دورية للبرمجيات</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="File Icon" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Document Icon" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="C# Icon" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Web Coding Icon" class="rotating-image" style="bottom: -3%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Code Icon" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Java Icon" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>تقنيات حديثة</h3>
                    <p>نستخدم أحدث لغات البرمجة والأطر التقنية لضمان أداء متميز ومستقبل مضمون</p>
                </div>
                <div class="feature-card">
                    <h3>تصميم مخصص</h3>
                    <p>كل مشروع يتم تصميمه خصيصاً ليناسب احتياجاتكم وطبيعة عملكم بدقة</p>
                </div>
                <div class="feature-card">
                    <h3>واجهات سهلة</h3>
                    <p>تصميم واجهات مستخدم بديهية وسهلة الاستخدام تحسن تجربة المستخدمين</p>
                </div>
                <div class="feature-card">
                    <h3>أمان عالي</h3>
                    <p>تطبيق أعلى معايير الأمان لحماية بياناتكم ومعلومات عملائكم</p>
                </div>
                <div class="feature-card">
                    <h3>قابلية التوسع</h3>
                    <p>برمجيات مرنة قابلة للتطوير والتوسع مع نمو أعمالكم</p>
                </div>
                <div class="feature-card">
                    <h3>دعم شامل</h3>
                    <p>دعم فني متواصل وتدريب شامل لفريقكم على استخدام الأنظمة</p>
                </div>
            </div>
        </section>
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل لديكم فكرة لتطبيق أو نظام؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصلوا معنا لتحويل أفكاركم إلى واقع رقمي متميز</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                showPageTransition('Switching to English...', 'جاري التبديل إلى الإنجليزية...', () => {
                    sessionStorage.setItem('pageTransition', 'true');
                    window.location.href = 'software-en.html';
                });
            }
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;
            document.body.appendChild(transition);
            setTimeout(() => transition.classList.add('active'), 50);
            setTimeout(() => callback(), 1200);
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
            } else {
                setTimeout(() => pageLoader.classList.add('fade-out'), 1500);
            }
            setTimeout(() => pageLoader.style.display = 'none', 2800);
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك في صفحة البرمجيات
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            let lastTimestamp = 0;
            const rotationSpeed = 0.5;

            function animate(timestamp) {
                if (!lastTimestamp) lastTimestamp = timestamp;

                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    lastTimestamp = timestamp;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    lastTimestamp = 0;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
                imageOrbit.style.transition = 'none';
            });
        });

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
