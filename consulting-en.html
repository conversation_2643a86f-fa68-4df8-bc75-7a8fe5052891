<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud Solutions - MCT</title>
    <meta name="description" content="Integrated cloud solutions that help reduce costs and increase flexibility">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                    <li><a href="consulting-en.html" class="active">Cloud Solutions</a></li>
                </ul>
            </div>
            
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-cloud"></i></div>
            <h1 class="service-title">Cloud Solutions</h1>
            <p class="service-subtitle">
                We provide integrated cloud solutions that help reduce costs and increase flexibility and efficiency in your business operations. 
                Move to the cloud with confidence and enjoy the benefits of modern technology.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section">
            <h2 class="section-title">Our Cloud Solutions Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>Cloud Migration:</strong> Safe and efficient migration of your systems and data to the cloud</li>
                        <li><strong>Cloud Infrastructure:</strong> Design and implementation of scalable cloud infrastructure</li>
                        <li><strong>Cloud Security:</strong> Advanced security solutions to protect your data in the cloud</li>
                        <li><strong>Cloud Backup:</strong> Reliable backup and disaster recovery solutions</li>
                        <li><strong>Cloud Monitoring:</strong> Continuous monitoring and optimization of cloud performance</li>
                        <li><strong>Cloud Consulting:</strong> Expert consultation to choose the best cloud strategy for your business</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="Cloud File" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Cloud Document" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="Cloud Code" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Cloud Web" class="rotating-image" style="bottom: -5%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Cloud Programming" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Cloud Java" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="content-section">
            <h2 class="section-title">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Cost Savings</h3>
                    <p>Reduce infrastructure costs and operational expenses with efficient cloud solutions</p>
                </div>
                <div class="feature-card">
                    <h3>High Flexibility</h3>
                    <p>Scale your resources up or down based on your business needs and demands</p>
                </div>
                <div class="feature-card">
                    <h3>Accessibility</h3>
                    <p>Access your data and applications from anywhere, anytime, on any device</p>
                </div>
                <div class="feature-card">
                    <h3>Automatic Updates</h3>
                    <p>Enjoy automatic updates and maintenance without worrying about system management</p>
                </div>
                <div class="feature-card">
                    <h3>Business Continuity</h3>
                    <p>Ensure business continuity with reliable backup and disaster recovery solutions</p>
                </div>
                <div class="feature-card">
                    <h3>Advanced Security</h3>
                    <p>Benefit from enterprise-grade security measures and compliance standards</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Want to Move to the Cloud?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free consultation on the best cloud solutions for your business</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // Particle System
        function createParticles() {
            const particleContainer = document.createElement('div');
            particleContainer.className = 'particles';
            document.body.appendChild(particleContainer);

            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'ar') {
                window.location.href = 'consulting.html';
            }
        }

        window.addEventListener('load', createParticles);
    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
</body>
</html>
