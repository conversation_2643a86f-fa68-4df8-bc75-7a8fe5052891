<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشبكات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop id="bgVideo" preload="auto">
            <source src="video/7021928_City_Smart_3840x2160%20.mp4" type="video/mp4">
            <source src="video/7021928_City_Smart_3840x2160 .mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </div>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html" class="active">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                    <li><a href="consulting.html">الحلول السحابية</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-network-wired"></i></div>
            <h1 class="service-title">حلول الشبكات</h1>
            <p class="service-subtitle">
                نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع أنظمتكم. 
                من التصميم والتنفيذ إلى الصيانة والدعم، نحن شريككم الموثوق في عالم الشبكات.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section">
            <h2 class="section-title">خدماتنا في الشبكات</h2>
            <div class="services-with-video">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>تصميم الشبكات:</strong> تصميم شبكات مخصصة تتناسب مع احتياجاتكم وحجم أعمالكم</li>
                        <li><strong>تنفيذ وتركيب:</strong> تنفيذ احترافي للشبكات باستخدام أحدث المعدات والتقنيات</li>
                        <li><strong>الشبكات اللاسلكية:</strong> حلول Wi-Fi متقدمة وآمنة لجميع البيئات</li>
                        <li><strong>شبكات الخوادم:</strong> إعداد وإدارة شبكات الخوادم عالية الأداء</li>
                        <li><strong>الصيانة الدورية:</strong> خدمات صيانة شاملة لضمان استمرارية الشبكة</li>
                        <li><strong>مراقبة الشبكة:</strong> مراقبة مستمرة للأداء واكتشاف المشاكل مبكراً</li>
                        <li><strong>ترقية الشبكات:</strong> تحديث وترقية الشبكات الموجودة لتحسين الأداء</li>
                    </ul>
                </div>
                <div class="services-video">
                    <video id="servicesVideo" muted loop preload="auto">
                        <source src="video/7021928_City_Smart_3840x2160 .mp4" type="video/mp4"style="width: 50%; height: 300px;">
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </section>

        <!-- المميزات -->
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>أداء عالي</h3>
                    <p>شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم</p>
                </div>
                <div class="feature-card">
                    <h3>موثوقية تامة</h3>
                    <p>حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال</p>
                </div>
                <div class="feature-card">
                    <h3>أمان متقدم</h3>
                    <p>تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح</p>
                </div>
                <div class="feature-card">
                    <h3>قابلية التوسع</h3>
                    <p>شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية</p>
                </div>
                <div class="feature-card">
                    <h3>دعم فني متميز</h3>
                    <p>فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات</p>
                </div>
                <div class="feature-card">
                    <h3>تكلفة مثلى</h3>
                    <p>حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لشبكة موثوقة وعالية الأداء؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثيرات الفيديو التفاعلية المحسنة للخلفية
        function setupVideoInteraction() {
            const video = document.getElementById('bgVideo');

            if (!video) {
                console.log('Background video element not found');
                return;
            }

            console.log('Setting up background video interaction...');

            let isMouseInside = true;
            let mouseTimeout;

            // إعدادات الفيديو
            video.muted = true;
            video.loop = true;
            video.autoplay = true;
            video.playsInline = true;

            // محاولة تشغيل الفيديو فوراً
            const playVideo = () => {
                video.play().then(() => {
                    console.log('Background video started playing');
                }).catch(e => {
                    console.log('Background video play failed:', e);
                    // محاولة أخرى بعد ثانية
                    setTimeout(() => {
                        video.play().catch(err => console.log('Background video second attempt failed:', err));
                    }, 1000);
                });
            };

            // تشغيل الفيديو عند تحميله
            video.addEventListener('loadeddata', playVideo);
            video.addEventListener('canplay', playVideo);

            // تشغيل فوري إذا كان الفيديو جاهز
            if (video.readyState >= 3) {
                playVideo();
            }

            // تشغيل الفيديو عند دخول الماوس للصفحة
            document.addEventListener('mouseenter', () => {
                isMouseInside = true;
                if (video.paused) {
                    playVideo();
                }
            });

            // إيقاف الفيديو عند خروج الماوس من الصفحة
            document.addEventListener('mouseleave', () => {
                isMouseInside = false;
                video.pause();
                video.style.transform = 'scale(1) translate(0px, 0px)';
            });

            // حركة الفيديو مع الماوس
            document.addEventListener('mousemove', (e) => {
                if (!isMouseInside) return;

                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // حركة أكثر سلاسة ووضوحاً
                const moveX = (mouseX - 0.5) * 20;
                const moveY = (mouseY - 0.5) * 20;

                video.style.transform = `scale(1.05) translate(${moveX}px, ${moveY}px)`;

                // إعادة تشغيل الفيديو إذا كان متوقفاً
                clearTimeout(mouseTimeout);
                if (video.paused && isMouseInside) {
                    playVideo();
                }

                // إيقاف الفيديو بعد 3 ثوان من عدم الحركة
                mouseTimeout = setTimeout(() => {
                    if (isMouseInside) {
                        video.pause();
                    }
                }, 3000);
            });

            // تشغيل الفيديو عند النقر على الصفحة
            document.addEventListener('click', playVideo, { once: true });

            // تشغيل الفيديو عند لمس الشاشة (للأجهزة المحمولة)
            document.addEventListener('touchstart', playVideo, { once: true });
        }

        // إعداد الفيديو في قسم الخدمات
        function setupServicesVideo() {
            const servicesVideo = document.getElementById('servicesVideo');
            const videoContainer = document.querySelector('.services-video');

            if (!servicesVideo || !videoContainer) {
                console.log('Services video element not found');
                return;
            }

            console.log('Setting up services video...');

            // إعدادات الفيديو
            servicesVideo.muted = true;
            servicesVideo.loop = true;
            servicesVideo.playsInline = true;

            // تشغيل الفيديو عند مرور الماوس
            videoContainer.addEventListener('mouseenter', () => {
                servicesVideo.play().then(() => {
                    console.log('Services video started playing');
                }).catch(e => {
                    console.log('Services video play failed:', e);
                });
            });

            // إيقاف الفيديو عند خروج الماوس
            videoContainer.addEventListener('mouseleave', () => {
                servicesVideo.pause();
                servicesVideo.currentTime = 0; // إعادة تعيين الفيديو للبداية
            });

            // تشغيل الفيديو عند النقر (للأجهزة التي تتطلب تفاعل المستخدم)
            videoContainer.addEventListener('click', () => {
                if (servicesVideo.paused) {
                    servicesVideo.play().catch(e => console.log('Services video click play failed:', e));
                }
            });
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            createParticles();
            setupVideoInteraction();
            setupServicesVideo();
        });


    </script>
    <script src="js/translation.js"></script>
</body>
</html>
