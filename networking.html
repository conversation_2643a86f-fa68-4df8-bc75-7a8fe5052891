<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشبكات - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- خلفية فيديو متحركة -->
    <div class="video-background">
        <video autoplay muted loop id="bgVideo">
            <source src="video/7021928_City_Smart_3840x2160 .mp4" type="video/mp4">
        </video>
    </div>
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="company-name">MCT</div>
            </div>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html" class="active">الشبكات</a></li>
                    <li><a href="software.html">البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الصيانة</a></li>
                    <li><a href="consulting.html">الحلول السحابية</a></li>
                </ul>
                <button id="lang-toggle">EN</button>
            </div>
            <div>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="service-hero">
            <div class="service-icon">🌐</div>
            <h1 class="service-title">الشبكات</h1>
            <p class="service-subtitle">
                نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع أنظمتكم. 
                من التصميم والتنفيذ إلى الصيانة والدعم، نحن شريككم الموثوق في عالم الشبكات.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section">
            <h2 class="section-title">خدماتنا في الشبكات</h2>
            <ul class="services-list">
                <li><strong>تصميم الشبكات:</strong> تصميم شبكات مخصصة تتناسب مع احتياجاتكم وحجم أعمالكم</li>
                <li><strong>تنفيذ وتركيب:</strong> تنفيذ احترافي للشبكات باستخدام أحدث المعدات والتقنيات</li>
                <li><strong>الشبكات اللاسلكية:</strong> حلول Wi-Fi متقدمة وآمنة لجميع البيئات</li>
                <li><strong>شبكات الخوادم:</strong> إعداد وإدارة شبكات الخوادم عالية الأداء</li>
                <li><strong>الصيانة الدورية:</strong> خدمات صيانة شاملة لضمان استمرارية الشبكة</li>
                <li><strong>مراقبة الشبكة:</strong> مراقبة مستمرة للأداء واكتشاف المشاكل مبكراً</li>
                <li><strong>ترقية الشبكات:</strong> تحديث وترقية الشبكات الموجودة لتحسين الأداء</li>
            </ul>
        </section>

        <!-- المميزات -->
        <section class="content-section">
            <h2 class="section-title">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>أداء عالي</h3>
                    <p>شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم</p>
                </div>
                <div class="feature-card">
                    <h3>موثوقية تامة</h3>
                    <p>حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال</p>
                </div>
                <div class="feature-card">
                    <h3>أمان متقدم</h3>
                    <p>تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح</p>
                </div>
                <div class="feature-card">
                    <h3>قابلية التوسع</h3>
                    <p>شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية</p>
                </div>
                <div class="feature-card">
                    <h3>دعم فني متميز</h3>
                    <p>فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات</p>
                </div>
                <div class="feature-card">
                    <h3>تكلفة مثلى</h3>
                    <p>حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج لشبكة موثوقة وعالية الأداء؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // تأثيرات الفيديو التفاعلية المحسنة
        function setupVideoInteraction() {
            const video = document.getElementById('bgVideo');
            const videoContainer = document.querySelector('.video-background');
            let isMouseInside = false;

            // تشغيل الفيديو عند دخول الماوس
            document.addEventListener('mouseenter', () => {
                isMouseInside = true;
                video.play();
            });

            // إيقاف الفيديو عند خروج الماوس
            document.addEventListener('mouseleave', () => {
                isMouseInside = false;
                video.pause();
                video.style.transform = 'scale(1.1) translate(0px, 0px)';
            });

            // حركة الفيديو مع الماوس
            document.addEventListener('mousemove', (e) => {
                if (!isMouseInside) return;

                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                // حركة أكثر سلاسة ووضوحاً
                const moveX = (mouseX - 0.5) * 30;
                const moveY = (mouseY - 0.5) * 30;

                video.style.transform = `scale(1.15) translate(${moveX}px, ${moveY}px)`;
            });

            // إيقاف الفيديو مؤقتاً عند عدم الحركة
            let mouseTimeout;
            document.addEventListener('mousemove', () => {
                clearTimeout(mouseTimeout);
                if (video.paused && isMouseInside) {
                    video.play();
                }

                mouseTimeout = setTimeout(() => {
                    if (isMouseInside) {
                        video.pause();
                    }
                }, 3000); // إيقاف بعد 3 ثوان من عدم الحركة
            });

            // تشغيل تلقائي عند تحميل الصفحة
            video.addEventListener('loadeddata', () => {
                video.play();
            });
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            createParticles();
            setupVideoInteraction();
        });

        // نظام الترجمة مع حفظ اللغة
        document.getElementById('lang-toggle').addEventListener('click', function() {
            const btn = this;
            const isArabic = document.documentElement.lang === 'ar';
            const newLang = isArabic ? 'en' : 'ar';

            // حفظ اللغة
            localStorage.setItem('selectedLanguage', newLang);

            document.documentElement.lang = newLang;
            btn.textContent = isArabic ? 'AR' : 'EN';

            const translations = {
                'الشبكات': 'Networking',
                'نقدم حلول شبكات متكاملة وموثوقة تضمن الاتصال السلس والآمن لجميع أنظمتكم. من التصميم والتنفيذ إلى الصيانة والدعم، نحن شريككم الموثوق في عالم الشبكات.': 'We provide integrated and reliable network solutions that ensure smooth and secure connectivity for all your systems. From design and implementation to maintenance and support, we are your trusted partner in the networking world.',
                'خدماتنا في الشبكات': 'Our Networking Services',
                'تصميم الشبكات: تصميم شبكات مخصصة تتناسب مع احتياجاتكم وحجم أعمالكم': 'Network Design: Custom network design that suits your needs and business size',
                'تنفيذ وتركيب: تنفيذ احترافي للشبكات باستخدام أحدث المعدات والتقنيات': 'Implementation and Installation: Professional network implementation using the latest equipment and technologies',
                'الشبكات اللاسلكية: حلول Wi-Fi متقدمة وآمنة لجميع البيئات': 'Wireless Networks: Advanced and secure Wi-Fi solutions for all environments',
                'شبكات الخوادم: إعداد وإدارة شبكات الخوادم عالية الأداء': 'Server Networks: Setup and management of high-performance server networks',
                'الصيانة الدورية: خدمات صيانة شاملة لضمان استمرارية الشبكة': 'Periodic Maintenance: Comprehensive maintenance services to ensure network continuity',
                'مراقبة الشبكة: مراقبة مستمرة للأداء واكتشاف المشاكل مبكراً': 'Network Monitoring: Continuous performance monitoring and early problem detection',
                'ترقية الشبكات: تحديث وترقية الشبكات الموجودة لتحسين الأداء': 'Network Upgrades: Updating and upgrading existing networks to improve performance',
                'لماذا تختار خدماتنا؟': 'Why Choose Our Services?',
                'أداء عالي': 'High Performance',
                'شبكات عالية السرعة والكفاءة تضمن أداءً مثالياً لجميع تطبيقاتكم وأنظمتكم': 'High-speed and efficient networks ensuring optimal performance for all your applications and systems',
                'موثوقية تامة': 'Complete Reliability',
                'حلول شبكات موثوقة مع ضمانات الجودة وخطط النسخ الاحتياطي للحماية من الأعطال': 'Reliable network solutions with quality guarantees and backup plans for protection against failures',
                'أمان متقدم': 'Advanced Security',
                'تطبيق أعلى معايير الأمان لحماية شبكتكم من التهديدات والوصول غير المصرح': 'Implementation of the highest security standards to protect your network from threats and unauthorized access',
                'قابلية التوسع': 'Scalability',
                'شبكات مرنة قابلة للتوسع والتطوير مع نمو أعمالكم ومتطلباتكم المستقبلية': 'Flexible networks that can be expanded and developed with your business growth and future requirements',
                'دعم فني متميز': 'Excellent Technical Support',
                'فريق دعم فني متخصص متاح على مدار الساعة لحل أي مشاكل أو استفسارات': 'Specialized technical support team available 24/7 to solve any problems or inquiries',
                'تكلفة مثلى': 'Optimal Cost',
                'حلول فعالة من حيث التكلفة تحقق أفضل عائد على الاستثمار لمشاريعكم': 'Cost-effective solutions that achieve the best return on investment for your projects',
                'هل تحتاج لشبكة موثوقة وعالية الأداء؟': 'Do You Need a Reliable and High-Performance Network?',
                'تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتكم من الشبكات': 'Contact us today for a free consultation and comprehensive assessment of your networking needs',
                'تواصل معنا': 'Contact Us',
                'اتصل بنا': 'Call Us',
                'العودة للرئيسية': 'Back to Home'
            };

            document.querySelectorAll('h1, h2, h3, h4, p, button, a, li').forEach(el => {
                const original = el.textContent.trim();

                if (el.id === 'lang-toggle' || original === 'MCT') {
                    return;
                }

                if (isArabic) {
                    if (translations[original]) {
                        el.textContent = translations[original];
                    }
                } else {
                    for (const [ar, en] of Object.entries(translations)) {
                        if (en === original) {
                            el.textContent = ar;
                            break;
                        }
                    }
                }
            });
        });

        // تحميل اللغة المحفوظة عند تحميل الصفحة
        window.addEventListener('load', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            if (savedLang !== document.documentElement.lang) {
                document.getElementById('lang-toggle').click();
            }
        });
    </script>
</body>
</html>
