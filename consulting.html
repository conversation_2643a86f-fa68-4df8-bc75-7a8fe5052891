<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاستشارات التقنية - MCT</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        :root { --primary-color: #28909A; --secondary-color: #1c5c62; --accent-color-1: #F7A600; --accent-color-2: #008B80; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e); color: #ffffff; overflow-x: hidden; position: relative; line-height: 1.6; }
        .animated-bg { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e); }
        .floating-particles { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; pointer-events: none; }
        .particle { position: absolute; width: 6px; height: 6px; background: rgb(0, 151, 139); border-radius: 50%; animation: float 8s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; } 50% { transform: translateY(-30px) rotate(180deg); opacity: 1; } }
        .header { background: rgba(15, 15, 35, 0.95); backdrop-filter: blur(20px); padding: 20px 5%; border-bottom: 1px solid rgba(255, 255, 255, 0.1); position: fixed; top: 0; width: 100%; z-index: 1000; }
        .header-content { display: flex; align-items: center; justify-content: space-between; max-width: 1200px; margin: 0 auto; }
        .logo-section { display: flex; align-items: center; gap: 15px; }
        .logo { width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; overflow: hidden; }
        .logo img { width: 100%; height: 100%; object-fit: contain; }
        .company-name { font-size: 24px; font-weight: bold; color: #ffffff; }
        .back-btn { padding: 10px 20px; background: linear-gradient(135deg, #008B80, #1c5c62); color: white; border: none; border-radius: 20px; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; }
        .back-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(0, 139, 128, 0.4); }
        .main-content { margin-top: 100px; padding: 50px 5%; max-width: 1200px; margin-left: auto; margin-right: auto; }
        .service-hero { text-align: center; margin-bottom: 80px; }
        .service-icon { width: 120px; height: 120px; background: linear-gradient(135deg, #008B80, #F7A600); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 30px; font-size: 50px; color: white; box-shadow: 0 20px 40px rgba(0, 139, 128, 0.3); animation: pulse 3s ease-in-out infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        .service-title { font-size: 3.5rem; margin-bottom: 20px; background: linear-gradient(135deg, #008B80, #F7A600); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .service-subtitle { font-size: 1.3rem; color: #b0b0b0; max-width: 800px; margin: 0 auto; }
        .content-section { margin-bottom: 60px; }
        .section-title { font-size: 2.2rem; color: #008B80; margin-bottom: 30px; text-align: center; position: relative; }
        .section-title::after { content: ''; position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 3px; background: linear-gradient(135deg, #008B80, #F7A600); border-radius: 2px; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 40px; }
        .feature-card { background: rgba(255, 255, 255, 0.08); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px; padding: 30px; transition: all 0.3s ease; position: relative; overflow: hidden; }
        .feature-card::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1)); transition: left 0.5s ease; z-index: -1; }
        .feature-card:hover::before { left: 0; }
        .feature-card:hover { transform: translateY(-10px); border-color: rgba(0, 139, 128, 0.3); box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2); }
        .feature-card h3 { font-size: 1.4rem; color: #F7A600; margin-bottom: 15px; }
        .feature-card p { color: #c0c0c0; line-height: 1.7; }
        .services-list { list-style: none; padding: 0; }
        .services-list li { background: rgba(255, 255, 255, 0.05); margin-bottom: 15px; padding: 20px; border-radius: 15px; border-left: 4px solid #008B80; transition: all 0.3s ease; }
        .services-list li:hover { background: rgba(0, 139, 128, 0.1); transform: translateX(10px); }
        .contact-cta { text-align: center; margin-top: 60px; padding: 40px; background: rgba(255, 255, 255, 0.05); border-radius: 25px; border: 1px solid rgba(255, 255, 255, 0.1); }
        .cta-button { padding: 15px 40px; background: linear-gradient(135deg, #008B80, #F7A600); color: white; border: none; border-radius: 30px; font-size: 1.1rem; font-weight: bold; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; margin: 10px; }
        .cta-button:hover { transform: translateY(-3px); box-shadow: 0 15px 40px rgba(0, 139, 128, 0.5); }
        @media (max-width: 768px) { .service-title { font-size: 2.5rem; } .features-grid { grid-template-columns: 1fr; } .main-content { padding: 30px 5%; } }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
                <div class="company-name">MCT</div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button id="lang-toggle" style="background: linear-gradient(135deg, #008B80, #1c5c62); color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: 500; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="service-hero">
            <div class="service-icon">💡</div>
            <h1 class="service-title">الاستشارات التقنية</h1>
            <p class="service-subtitle">نقدم استشارات تقنية متخصصة لمساعدتكم في اتخاذ القرارات الصحيحة وتطوير استراتيجيات تقنية فعالة. خبراؤنا يقدمون حلولاً مبتكرة تتماشى مع أهدافكم.</p>
        </section>
        <section class="content-section">
            <h2 class="section-title">خدمات الاستشارات التقنية</h2>
            <ul class="services-list">
                <li><strong>استشارات التحول الرقمي:</strong> وضع استراتيجيات شاملة للتحول الرقمي</li>
                <li><strong>تقييم البنية التحتية:</strong> تحليل وتقييم الأنظمة التقنية الحالية</li>
                <li><strong>اختيار التقنيات:</strong> المساعدة في اختيار أفضل التقنيات والحلول</li>
                <li><strong>تخطيط المشاريع:</strong> وضع خطط تنفيذ مفصلة للمشاريع التقنية</li>
                <li><strong>إدارة المخاطر:</strong> تحديد وإدارة المخاطر التقنية والأمنية</li>
                <li><strong>التدريب والتطوير:</strong> برامج تدريبية متخصصة للفرق التقنية</li>
                <li><strong>الدعم الاستراتيجي:</strong> مشورة مستمرة في القرارات التقنية المهمة</li>
            </ul>
        </section>
        <section class="content-section">
            <h2 class="section-title">لماذا تحتاجون للاستشارات التقنية؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>خبرة متخصصة</h3>
                    <p>الاستفادة من خبرة واسعة في مختلف المجالات التقنية والصناعات</p>
                </div>
                <div class="feature-card">
                    <h3>توفير التكاليف</h3>
                    <p>تجنب الأخطاء المكلفة واتخاذ قرارات مدروسة توفر الوقت والمال</p>
                </div>
                <div class="feature-card">
                    <h3>رؤية محايدة</h3>
                    <p>الحصول على تقييم موضوعي ومحايد للوضع التقني الحالي</p>
                </div>
                <div class="feature-card">
                    <h3>أفضل الممارسات</h3>
                    <p>تطبيق أفضل الممارسات العالمية في مجال التكنولوجيا</p>
                </div>
                <div class="feature-card">
                    <h3>التخطيط المستقبلي</h3>
                    <p>وضع استراتيجيات طويلة المدى تواكب التطورات التقنية</p>
                </div>
                <div class="feature-card">
                    <h3>تسريع النمو</h3>
                    <p>تسريع عملية التطوير والنمو من خلال الحلول المناسبة</p>
                </div>
            </div>
        </section>
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاجون لاستشارة تقنية متخصصة؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">احجزوا جلسة استشارية مجانية مع خبرائنا لمناقشة احتياجاتكم التقنية</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        window.addEventListener('load', createParticles);

        // نظام الترجمة مع حفظ اللغة
        document.getElementById('lang-toggle').addEventListener('click', function() {
            const btn = this;
            const isArabic = document.documentElement.lang === 'ar';
            const newLang = isArabic ? 'en' : 'ar';

            // حفظ اللغة
            localStorage.setItem('selectedLanguage', newLang);

            document.documentElement.lang = newLang;
            btn.textContent = isArabic ? 'AR' : 'EN';

            const translations = {
                'الاستشارات التقنية': 'Technical Consulting',
                'نقدم استشارات تقنية متخصصة لمساعدتكم في اتخاذ القرارات الصحيحة وتطوير استراتيجيات تقنية فعالة. خبراؤنا يقدمون حلولاً مبتكرة تتماشى مع أهدافكم.': 'We provide specialized technical consulting to help you make the right decisions and develop effective technical strategies. Our experts provide innovative solutions that align with your goals.',
                'خدمات الاستشارات التقنية': 'Technical Consulting Services',
                'استشارات التحول الرقمي: وضع استراتيجيات شاملة للتحول الرقمي': 'Digital Transformation Consulting: Developing comprehensive digital transformation strategies',
                'تقييم البنية التحتية: تحليل وتقييم الأنظمة التقنية الحالية': 'Infrastructure Assessment: Analysis and evaluation of current technical systems',
                'اختيار التقنيات: المساعدة في اختيار أفضل التقنيات والحلول': 'Technology Selection: Assistance in choosing the best technologies and solutions',
                'تخطيط المشاريع: وضع خطط تنفيذ مفصلة للمشاريع التقنية': 'Project Planning: Developing detailed implementation plans for technical projects',
                'إدارة المخاطر: تحديد وإدارة المخاطر التقنية والأمنية': 'Risk Management: Identifying and managing technical and security risks',
                'التدريب والتطوير: برامج تدريبية متخصصة للفرق التقنية': 'Training and Development: Specialized training programs for technical teams',
                'الدعم الاستراتيجي: مشورة مستمرة في القرارات التقنية المهمة': 'Strategic Support: Continuous advice on important technical decisions',
                'لماذا تحتاجون للاستشارات التقنية؟': 'Why Do You Need Technical Consulting?',
                'خبرة متخصصة': 'Specialized Expertise',
                'الاستفادة من خبرة واسعة في مختلف المجالات التقنية والصناعات': 'Benefit from extensive experience in various technical fields and industries',
                'توفير التكاليف': 'Cost Savings',
                'تجنب الأخطاء المكلفة واتخاذ قرارات مدروسة توفر الوقت والمال': 'Avoid costly mistakes and make informed decisions that save time and money',
                'رؤية محايدة': 'Neutral Perspective',
                'الحصول على تقييم موضوعي ومحايد للوضع التقني الحالي': 'Get an objective and neutral assessment of the current technical situation',
                'أفضل الممارسات': 'Best Practices',
                'تطبيق أفضل الممارسات العالمية في مجال التكنولوجيا': 'Implementation of global best practices in technology',
                'التخطيط المستقبلي': 'Future Planning',
                'وضع استراتيجيات طويلة المدى تواكب التطورات التقنية': 'Developing long-term strategies that keep pace with technical developments',
                'تسريع النمو': 'Accelerate Growth',
                'تسريع عملية التطوير والنمو من خلال الحلول المناسبة': 'Accelerate development and growth through appropriate solutions',
                'هل تحتاجون لاستشارة تقنية متخصصة؟': 'Do You Need Specialized Technical Consulting?',
                'احجزوا جلسة استشارية مجانية مع خبرائنا لمناقشة احتياجاتكم التقنية': 'Book a free consultation session with our experts to discuss your technical needs',
                'تواصل معنا': 'Contact Us',
                'اتصل بنا': 'Call Us',
                'العودة للرئيسية': 'Back to Home'
            };

            document.querySelectorAll('h1, h2, h3, h4, p, button, a, li').forEach(el => {
                const original = el.textContent.trim();

                if (el.id === 'lang-toggle' || original === 'MCT') {
                    return;
                }

                if (isArabic) {
                    if (translations[original]) {
                        el.textContent = translations[original];
                    }
                } else {
                    for (const [ar, en] of Object.entries(translations)) {
                        if (en === original) {
                            el.textContent = ar;
                            break;
                        }
                    }
                }
            });
        });

        // تحميل اللغة المحفوظة عند تحميل الصفحة
        window.addEventListener('load', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            if (savedLang !== document.documentElement.lang) {
                document.getElementById('lang-toggle').click();
            }
        });
    </script>
</body>
</html>
