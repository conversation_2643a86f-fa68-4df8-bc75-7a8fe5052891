// نظام الشكل المتحرك للوحة التحكم
class DashboardAnimation {
    constructor() {
        this.init();
    }

    init() {
        // التحقق من وجود العناصر المطلوبة
        const dashboardContainer = document.querySelector('.dashboard-container');
        const imageOrbit = document.querySelector('.image-orbit');
        
        if (!dashboardContainer || !imageOrbit) {
            return; // لا توجد عناصر للتحريك
        }
        
        this.setupAnimation(dashboardContainer, imageOrbit);
    }

    setupAnimation(dashboardContainer, imageOrbit) {
        let currentRotation = 0;
        let isRotating = false;
        let animationFrame;
        const rotationSpeed = 0.5;

        function animate() {
            if (isRotating) {
                currentRotation += rotationSpeed;
                if (currentRotation >= 360) {
                    currentRotation = currentRotation % 360;
                }
                imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                animationFrame = requestAnimationFrame(animate);
            }
        }

        // بدء الحركة عند مرور الماوس
        dashboardContainer.addEventListener('mouseenter', function() {
            if (!isRotating) {
                isRotating = true;
                animationFrame = requestAnimationFrame(animate);
            }
        });

        // إيقاف الحركة عند خروج الماوس
        dashboardContainer.addEventListener('mouseleave', function() {
            isRotating = false;
            cancelAnimationFrame(animationFrame);
        });
    }

    // إضافة الشكل المتحرك لصفحة معينة
    static addToPage(containerId = 'services-animation-container') {
        const container = document.getElementById(containerId);
        if (!container) return;

        const animationHTML = `
            <div class="services-animation">
                <div class="dashboard-container">
                    <div class="rotating-images">
                        <div class="image-orbit">
                            <!-- Top -->
                            <img src="image/file_11634597.png" alt="File Icon" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                            <!-- Top Right -->
                            <img src="image/document_11665380.png" alt="Document Icon" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                            <!-- Bottom Right -->
                            <img src="image/c-sharp_6132221.png" alt="C# Icon" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                            <!-- Bottom -->
                            <img src="image/web-coding_11513813.png" alt="Web Coding Icon" class="rotating-image" style="bottom: -5%; left: 50%; transform: translate(-50%, 50%);">
                            <!-- Bottom Left -->
                            <img src="image/code_4997543.png" alt="Code Icon" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                            <!-- Top Left -->
                            <img src="image/java_3291669.png" alt="Java Icon" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                        </div>
                    </div>
                    <!-- Main Laptop -->
                    <div class="laptop">
                        <div class="laptop-screen">
                            <div class="screen-content">
                                <div class="window-bar">
                                    <div class="window-buttons">
                                        <div class="window-button"></div>
                                        <div class="window-button"></div>
                                        <div class="window-button"></div>
                                    </div>
                                </div>
                                <div class="content-area">
                                    <div class="left-panel">
                                        <div class="blue-section"></div>
                                        <div class="green-section"></div>
                                    </div>
                                    <div class="right-panel">
                                        <div class="data-lines">
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                            <div class="line"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Connection Lines -->
                    <div class="connection-lines">
                        <div class="connection-line line-1"></div>
                        <div class="connection-line line-2"></div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = animationHTML;
        
        // تهيئة الحركة بعد إضافة HTML
        setTimeout(() => {
            new DashboardAnimation();
        }, 100);
    }
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new DashboardAnimation();
});

// تصدير الكلاس للاستخدام العام
window.DashboardAnimation = DashboardAnimation;
