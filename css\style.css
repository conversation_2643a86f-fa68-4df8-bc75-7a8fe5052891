/* ===================================
   MCT Website - Main Stylesheet
   ===================================*/

/* ===================================
   GLOBAL STYLES & RESET
   ===================================*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #28909A;
    --secondary-color: #1c5c62;
    --accent-color-1: #F7A600;
    --accent-color-2: #008B80;
    --text-color: #ffffff;
    --bg-dark: #0f0f23;
    --card-bg: rgba(255, 255, 255, 0.05);
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
    line-height: 1.6;
}

/* ===================================
   BACKGROUND ANIMATIONS
   ===================================*/
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
}

.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgb(0, 151, 139);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.5; 
    }
    50% { 
        transform: translateY(-20px) rotate(180deg); 
        opacity: 1; 
    }
}

/* ===================================
   VIDEO BACKGROUND (for Networks page)
   ===================================*/
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    overflow: hidden;
}

.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: scale(1);
}

.video-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 15, 35, 0.6);
    z-index: 1;
    pointer-events: none;
}

/* ===================================
   NAVIGATION BAR
   ===================================*/
.navbar, .header {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 20px 5%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container, .header-content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    justify-content: space-between;
    padding: 0 20px;
}

/* Updated navbar layout for centered language toggle */
.nav-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
    animation: logoGlow 3s ease-in-out infinite alternate;
    overflow: hidden;
}

.logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

@keyframes logoGlow {
    0% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(40, 144, 154, 0.6); }
}

.company-name {
    font-size: 26px;
    font-weight: bold;
    background: linear-gradient(135deg, #ffffff, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    gap: 20px;
    list-style: none;
    margin: 0;
}

.nav-links a {
    position: relative;
    display: inline-block;
    overflow: hidden;
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    padding: 12px 22px;
    border-radius: 25px;
    transition: var(--transition);
}

.nav-links a:hover {
    color: #ffffff;
    transform: scale(1.05);
}

.nav-links a::after {
    content: "";
    position: absolute;
    bottom: 8px;
    right: 20px;
    width: 0%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.4s ease;
}

.nav-links a:hover::after {
    width: 60%;
}

/* Service navigation for service pages */
.service-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    list-style: none;
    margin: -100px;
    padding: 0px;
    justify-content: center;
    align-items: center;
    width: 120%;
    max-width: 1100px;
}

.service-nav li {
    margin: 0;
    flex: 1;
    min-width: 100px;
}

.service-nav a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 12px;
    border-radius: 20px;
    transition: var(--transition);
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    display: block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 120px;
}

.service-nav a:hover {
    background: rgba(0, 139, 128, 0.2);
    border-color: var(--accent-color-2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 139, 128, 0.3);
}

.service-nav a.active {
    background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
    border-color: var(--accent-color-2);
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 139, 128, 0.4);
}

/* Language toggle button styling */
#lang-toggle {
    background: linear-gradient(135deg, var(--accent-color-1), #e6940a);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
    margin-left: 25px;
    border: 1px solid rgba(247, 166, 0, 0.3);
    box-shadow: 0 4px 12px rgba(247, 166, 0, 0.3);
   display: flex;
   align-items: center;
   justify-content: center;
    text-align: center;
}

#lang-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(247, 166, 0, 0.5);
    background: linear-gradient(135deg, #e6940a, var(--accent-color-1));
}

.back-btn {
    padding: 10px 20px;
    background: linear-gradient(135deg, #008B80, #1c5c62);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 139, 128, 0.4);
}

.nav-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

/* ===================================
   MAIN CONTENT LAYOUT
   ===================================*/
.main-content {
    margin-top: 100px;
    padding: 0 2%;
    width: 100%;
}

.section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    width: 100%;
    margin: 0 auto;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 50px;
    text-align: center;
    background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    padding-bottom: 20px;
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(to right, var(--accent-color-2), var(--secondary-color));
    border-radius: 2px;
}

/* ===================================
   HERO SECTION
   ===================================*/
.hero-section {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
}

.hero-logo {
    width: 200px;
    height: 200px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: heroFloat 3s ease-in-out infinite;
    overflow: hidden;
}

.hero-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

@keyframes heroFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textShine 3s ease-in-out infinite;
}

@keyframes textShine {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.hero-content p {
    font-size: 1.3rem;
    line-height: 1.8;
    max-width: 800px;
    color: #b0b0b0;
    margin-bottom: 30px;
}

/* ===================================
   SERVICE PAGES HERO
   ===================================*/
.service-hero {
    text-align: center;
    margin-bottom: 80px;
}

.service-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 50px;
    color: white;
    box-shadow: 0 20px 40px rgba(0, 139, 128, 0.3);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.service-title {
    font-size: 3.5rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-1));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.service-subtitle {
    font-size: 1.3rem;
    color: #b0b0b0;
    max-width: 800px;
    margin: 0 auto;
}

/* ===================================
   BUTTONS & CTA
   ===================================*/
.cta-button {
    padding: 15px 40px;
    background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 10px 30px rgba(40, 144, 154, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
    text-decoration: none;
    display: inline-block;
    margin: 10px;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color-1));
    transition: var(--transition);
    z-index: -1;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(40, 144, 154, 0.5);
}

.cta-button:hover::before {
    left: 0;
}

/* ===================================
   SERVICES SECTION
   ===================================*/
.services-section {
    flex-direction: column;
    text-align: center;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
}

.service-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    color: inherit;
    text-decoration: none;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(40, 144, 154, 0.1), rgba(118, 75, 162, 0.1));
    transition: left 0.5s ease;
    z-index: -1;
}

.service-card:hover::before {
    left: 0;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(40, 144, 154, 0.3);
    box-shadow: 0 20px 40px rgba(40, 144, 154, 0.2);
}

.service-card .service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color-2), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 30px;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--accent-color-2);
}

.service-card p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* ===================================
   CONTENT SECTIONS
   ===================================*/
.content-section {
    margin-bottom: 60px;
}

.content-section .section-title {
    font-size: 2.2rem;
    color: var(--accent-color-2);
    margin-bottom: 30px;
    text-align: center;
    position: relative;
}

.content-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-1));
    border-radius: 2px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 139, 128, 0.1), rgba(247, 166, 0, 0.1));
    transition: left 0.5s ease;
    z-index: -1;
}

.feature-card:hover::before {
    left: 0;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 139, 128, 0.3);
    box-shadow: 0 20px 40px rgba(0, 139, 128, 0.2);
}

.feature-card h3 {
    font-size: 1.4rem;
    color: var(--accent-color-1);
    margin-bottom: 15px;
}

.feature-card p {
    color: #c0c0c0;
    line-height: 1.7;
}

.services-list {
    list-style: none;
    padding: 0;
}

.services-list li {
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 15px;
    width: 50%;
    padding: 20px;
    border-radius: 20px;
    border-left: 4px solid var(--accent-color-2);
    transition: all 0.3s ease;
}

.services-list li:hover {
    background: rgba(0, 139, 128, 0.1);
    transform: translateX(10px);
}

/* ===================================
   CONTACT SECTION (Enhanced for horizontal layout)
   ===================================*/
.contact-section {
    flex-direction: column;
    text-align: center;
}

.contact-container {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    padding: 60px 40px;
    max-width: 2400px; /* Increased width for horizontal layout */
    width: 100%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    margin: 0 auto;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Wider minimum for horizontal layout */
    gap: 50px; /* Increased gap for better spacing */
    margin-bottom: 40px;
    align-items: start;
}

.contact-item {
    text-align: center;
    padding: 20px;
}

.contact-item h4 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.contact-item p {
    color: #b0b0b0;
    line-height: 1.6;
    margin-bottom: 10px;
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: var(--accent-color-2);
    text-decoration: underline;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.social-link {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    font-size: 20px;
    transition: var(--transition);
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 25px rgba(40, 144, 154, 0.5);
}

.contact-cta {
    text-align: center;
    margin-top: 60px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-media-heading {
    color: var(--accent-color-2);
    margin: 30px 0 20px;
}

/* ===================================
   TEAM SECTION
   ===================================*/
.team-section {
    flex-direction: column;
    text-align: center;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    width: 100%;
}

.team-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: var(--transition);
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.team-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto 20px;
    border: 3px solid var(--accent-color-2);
    object-fit: cover;
}

.team-card h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

.team-card .position {
    color: var(--accent-color-2);
    font-weight: 600;
    margin-bottom: 15px;
    display: block;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: var(--transition);
}

.social-icon:hover {
    background: var(--accent-color-2);
    transform: translateY(-3px);
}

/* ===================================
   PROJECTS SECTION
   ===================================*/
.projects-section {
    flex-direction: column;
    text-align: center;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    width: 100%;
}

.project-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    display: block;
}

.project-content {
    padding: 25px;
    text-align: right;
}

.project-card h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

.project-card .category {
    color: var(--accent-color-2);
    font-weight: 600;
    margin-bottom: 15px;
    display: block;
}

.project-card p {
    color: #b0b0b0;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* ===================================
   BLOG SECTION
   ===================================*/
.blog-section {
    flex-direction: column;
    text-align: center;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    width: 100%;
}

.blog-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition);
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.blog-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.blog-content {
    padding: 25px;
    text-align: right;
}

.blog-date {
    color: var(--accent-color-2);
    font-weight: 600;
    margin-bottom: 10px;
    display: block;
}

.blog-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--text-color);
}

.blog-card p {
    color: #b0b0b0;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* ===================================
   CLIENTS SECTION
   ===================================*/
.clients-section {
    flex-direction: column;
    text-align: center;
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    width: 100%;
    align-items: center;
}

.client-logo {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    transition: var(--transition);
}

.client-logo:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(40, 144, 154, 0.2);
}

.client-logo img {
    max-width: 100%;
    max-height: 60px;
    filter: grayscale(100%);
    transition: var(--transition);
}

.client-logo:hover img {
    filter: grayscale(0%);
}

/* ===================================
   FOOTER
   ===================================*/
.footer {
    background: rgba(15, 15, 35, 0.95);
    padding: 40px 5%;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.footer-logo {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
    overflow: hidden;
}

.footer-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.footer-links {
    display: flex;
    gap: 25px;
    margin: 20px 0;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-links a {
    color: #b0b0b0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-color-2);
}

.copyright {
    color: #777;
    font-size: 0.9rem;
    margin-top: 20px;
}

/* ===================================
   SCROLL TO TOP BUTTON
   ===================================*/
.scroll-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--accent-color-2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: var(--transition);
    z-index: 999;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.scroll-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-top:hover {
    background: var(--accent-color-1);
    transform: translateY(-5px);
}

/* ===================================
   FADE IN ANIMATIONS
   ===================================*/
.fade-in-element {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition);
}

.fade-in-element.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-element.delay-1 { transition-delay: 0.2s; }
.fade-in-element.delay-2 { transition-delay: 0.4s; }
.fade-in-element.delay-3 { transition-delay: 0.6s; }
.fade-in-element.delay-4 { transition-delay: 0.8s; }
.fade-in-element.delay-5 { transition-delay: 1s; }
.fade-in-element.delay-6 { transition-delay: 1.2s; }

/* ===================================
   METEOR EFFECTS
   ===================================*/
.meteor {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    box-shadow: 0 0 16px 6px white;
    transform: rotate(35deg);
    animation: shoot 6s linear infinite;
    opacity: 0;
    z-index: -1;
}

@keyframes shoot {
    0% {
        top: +0%;
        left: +0%;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        top: 60%;
        left: 70%;
        opacity: 0.6;
    }
    100% {
        top: 110%;
        left: 110%;
        opacity: 0;
    }
}

/* ===================================
   RESPONSIVE DESIGN
   ===================================*/
@media (max-width: 1200px) {
    .nav-container, .header-content {
        max-width: 1200px;
        padding: 0 15px;
    }

    .service-nav {
        gap: 6px;
        max-width: 700px;
    }

    .service-nav li {
        min-width: 100px;
    }

    .service-nav a {
        padding: 8px 10px;
        font-size: 0.8rem;
    }

    .contact-container {
        max-width: 1800px;
    }

    .contact-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 40px;
    }
}

@media (max-width: 992px) {
    .nav-links {
        gap: 15px;
    }

    .nav-links a {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .hero-content h1 {
        font-size: 2.8rem;
    }

    .service-nav {
        gap: 6px;
        max-width: 600px;
    }

    .service-nav li {
        min-width: 90px;
    }

    .service-nav a {
        padding: 8px 6px;
        font-size: 0.75rem;
    }

    #lang-toggle {
        padding: 8px 15px;
        font-size: 0.8rem;
        margin-left: 15px;
    }

    .contact-container {
        max-width: 1400px;
    }
}

@media (max-width: 768px) {
    .nav-container, .header-content {
        flex-direction: column;
        gap: 20px;
        padding: 15px 5%;
    }

    .nav-center {
        position: static;
        transform: none;
        flex-direction: column;
        gap: 20px;
        width: 100%;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .service-nav {
        gap: 4px;
        width: 100%;
        max-width: 100%;
    }

    .service-nav li {
        min-width: 80px;
        flex: 1;
    }

    .service-nav a {
        padding: 8px 4px;
        font-size: 0.7rem;
    }

    #lang-toggle {
        margin-left: 0;
        margin-top: 10px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .service-title {
        font-size: 2.5rem;
    }

    .services-grid,
    .projects-grid,
    .blog-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .contact-container {
        padding: 40px 20px;
        max-width: 100%;
    }

    .section {
        min-height: auto;
        padding: 80px 0;
    }

    .main-content {
        padding: 30px 5%;
        margin-top: 120px;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .service-title {
        font-size: 2rem;
    }

    .contact-container {
        padding: 30px 15px;
    }

    .nav-links a {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    #lang-toggle {
        padding: 8px 15px;
        font-size: 0.8rem;
    }

    .back-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .service-nav {
        flex-direction: column;
        gap: 6px;
        max-width: 100%;
    }

    .service-nav li {
        min-width: auto;
        width: 100%;
    }

    .service-nav a {
        padding: 8px 12px;
        font-size: 0.75rem;
    }
}
