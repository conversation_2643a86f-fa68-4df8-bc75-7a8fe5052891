<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Security Assessment</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html" class="active">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>

                </ul>
            </div>
            
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
            <h1 class="service-title fade-in-element delay-1">Security Assessment</h1>
            <p class="service-subtitle fade-in-element delay-2">
                We provide comprehensive security assessment services to ensure the protection of your systems and data. Our team of experts conducts a thorough assessment of your security infrastructure and provides effective recommendations.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">Our Security Assessment Services</h2>
            <ul class="services-list fade-in-element delay-1">
                <li><strong>Risk Assessment:</strong> Comprehensive analysis of security risks and vulnerability identification</li>
                <li><strong>Penetration Testing:</strong> Simulated attacks to test protection strength</li>
                <li><strong>Policy Review:</strong> Evaluation and development of cybersecurity policies</li>
                <li><strong>Infrastructure Assessment:</strong> Comprehensive inspection of systems, networks, and devices</li>
                <li><strong>Application Assessment:</strong> Security testing of applications and software</li>
                <li><strong>Compliance Assessment:</strong> Verification of system compliance with global security standards</li>
                <li><strong>Security Awareness Assessment:</strong> Measuring employee security awareness levels</li>
            </ul>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>Specialized Expertise</h3>
                    <p>A team of certified cybersecurity experts</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>Comprehensive Methodology</h3>
                    <p>Thorough assessment covering all aspects of cybersecurity</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>Advanced Technologies</h3>
                    <p>Use of the latest tools and technologies in security assessment</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>Practical Recommendations</h3>
                    <p>Actionable recommendations to improve cybersecurity</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>Detailed Reports</h3>
                    <p>Comprehensive reports with detailed risk analysis and solutions</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>Continuous Support</h3>
                    <p>Ongoing follow-up and training to ensure implementation of recommendations</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Need a Security Assessment for Your Systems?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a free consultation and comprehensive assessment of your security needs</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'security-assessment.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });


    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
