<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Assessment - MCT</title>
    <meta name="description" content="Comprehensive evaluation of your systems' security to identify vulnerabilities">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html" class="active">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>
                    <li><a href="consulting-en.html">Cloud Solutions</a></li>
                </ul>
            </div>
            
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-search"></i></div>
            <h1 class="service-title">Security Assessment</h1>
            <p class="service-subtitle">
                We provide comprehensive evaluation of your systems' security to identify vulnerabilities and provide improvement recommendations. 
                Our detailed assessments help you understand your security posture and strengthen your defenses.
            </p>
        </section>

        <!-- Our Services -->
        <section class="content-section">
            <h2 class="section-title">Our Security Assessment Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>Vulnerability Assessment:</strong> Comprehensive scanning and identification of security vulnerabilities</li>
                        <li><strong>Penetration Testing:</strong> Simulated attacks to test your system's defenses</li>
                        <li><strong>Security Audits:</strong> Detailed review of security policies and procedures</li>
                        <li><strong>Risk Analysis:</strong> Assessment of potential risks and their impact on your business</li>
                        <li><strong>Compliance Testing:</strong> Verification of compliance with security standards and regulations</li>
                        <li><strong>Security Reports:</strong> Detailed reports with findings and recommendations for improvement</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="Assessment File" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Assessment Document" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="Assessment Code" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Assessment Web" class="rotating-image" style="bottom: -5%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Assessment Programming" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Assessment Java" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="content-section">
            <h2 class="section-title">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Comprehensive Assessment</h3>
                    <p>Thorough evaluation of all aspects of your security infrastructure and processes</p>
                </div>
                <div class="feature-card">
                    <h3>Expert Analysis</h3>
                    <p>Assessment conducted by certified security professionals with extensive experience</p>
                </div>
                <div class="feature-card">
                    <h3>Detailed Reports</h3>
                    <p>Comprehensive reports with clear findings and actionable recommendations</p>
                </div>
                <div class="feature-card">
                    <h3>Industry Standards</h3>
                    <p>Assessment based on international security standards and best practices</p>
                </div>
                <div class="feature-card">
                    <h3>Risk Prioritization</h3>
                    <p>Clear prioritization of risks based on their potential impact on your business</p>
                </div>
                <div class="feature-card">
                    <h3>Ongoing Support</h3>
                    <p>Continuous support to help you implement recommended security improvements</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do You Want to Assess the Security of Your Systems?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us today for a comprehensive security assessment and detailed evaluation of your security posture</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>

    <script>
        // Particle System
        function createParticles() {
            const particleContainer = document.createElement('div');
            particleContainer.className = 'particles';
            document.body.appendChild(particleContainer);

            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Language Switch Function
        function switchLanguage(lang) {
            if (lang === 'ar') {
                window.location.href = 'security-assessment.html';
            }
        }

        window.addEventListener('load', createParticles);
    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
</body>
</html>
