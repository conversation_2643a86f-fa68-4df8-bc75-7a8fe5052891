<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفحص الأمني - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Security Assessment</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html" class="active">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-shield-alt"></i></div>
            <h1 class="service-title fade-in-element delay-1">التقييم الأمني</h1>
            <p class="service-subtitle fade-in-element delay-2">
                نقدم خدمات تقييم أمني شاملة لضمان حماية أنظمتكم وبياناتكم. فريقنا من الخبراء يقوم بتقييم شامل للبنية التحتية الأمنية وتقديم توصيات فعالة.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">خدمات التقييم الأمني لدينا</h2>
            <ul class="services-list fade-in-element delay-1">
                <li><strong>تقييم المخاطر:</strong> تحليل شامل للمخاطر الأمنية وتحديد نقاط الضعف</li>
                <li><strong>اختبار الاختراق:</strong> محاكاة هجمات افتراضية لاختبار قوة الحماية</li>
                <li><strong>مراجعة السياسات:</strong> تقييم وتطوير سياسات الأمن السيبراني</li>
                <li><strong>تقييم البنية التحتية:</strong> فحص شامل للأنظمة والشبكات والأجهزة</li>
                <li><strong>تقييم التطبيقات:</strong> فحص أمني للتطبيقات والبرمجيات</li>
                <li><strong>تقييم الامتثال:</strong> التحقق من توافق الأنظمة مع معايير الأمن العالمية</li>
                <li><strong>تقييم الوعي الأمني:</strong> قياس مستوى الوعي الأمني لدى الموظفين</li>
            </ul>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>خبرة متخصصة</h3>
                    <p>فريق من الخبراء المعتمدين في مجال الأمن السيبراني</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>منهجية شاملة</h3>
                    <p>تقييم شامل يغطي جميع جوانب الأمن السيبراني</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>تقنيات متطورة</h3>
                    <p>استخدام أحدث الأدوات والتقنيات في التقييم الأمني</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>توصيات عملية</h3>
                    <p>توصيات قابلة للتطبيق لتحسين الأمن السيبراني</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>تقارير مفصلة</h3>
                    <p>تقارير شاملة مع تحليل مفصل للمخاطر والحلول</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>دعم مستمر</h3>
                    <p>متابعة وتدريب مستمر لضمان تطبيق التوصيات</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج إلى تقييم أمني لأنظمتك؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتك الأمنية</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'security-assessment-en.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });


    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
