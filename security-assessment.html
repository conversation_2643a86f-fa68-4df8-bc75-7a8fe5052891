<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفحص الأمني - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <li><a href="security-assessment.html" class="active">الفحص الأمني</a></li>
                    <li><a href="maintenance.html">الدعم والصيانة</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="service-hero">
            <div class="service-icon">🔎</div>
            <h1 class="service-title">الفحص الأمني</h1>
            <p class="service-subtitle">نقدم خدمات فحص أمني شاملة لاكتشاف الثغرات والمخاطر في أنظمتكم قبل أن يستغلها المهاجمون. فريقنا من خبراء الأمن يستخدم أحدث الأدوات والتقنيات.</p>
        </section>
        <section class="content-section">
            <h2 class="section-title">خدمات الفحص الأمني</h2>
            <ul class="services-list">
                <li><strong>فحص اختراق الشبكات:</strong> تقييم شامل لأمان الشبكات واكتشاف نقاط الضعف</li>
                <li><strong>فحص التطبيقات:</strong> اختبار أمان تطبيقات الويب والهاتف المحمول</li>
                <li><strong>تقييم البنية التحتية:</strong> فحص الخوادم والأنظمة والمعدات الشبكية</li>
                <li><strong>اختبار الهندسة الاجتماعية:</strong> تقييم مدى وعي الموظفين بالتهديدات الأمنية</li>
                <li><strong>فحص قواعد البيانات:</strong> تقييم أمان قواعد البيانات وحماية المعلومات الحساسة</li>
                <li><strong>تقارير مفصلة:</strong> تقارير شاملة مع التوصيات وخطط المعالجة</li>
                <li><strong>إعادة الفحص:</strong> التحقق من إصلاح الثغرات المكتشفة</li>
            </ul>
        </section>
        <section class="content-section">
            <h2 class="section-title">لماذا الفحص الأمني مهم؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>اكتشاف مبكر</h3>
                    <p>اكتشاف الثغرات والمخاطر قبل أن يستغلها المهاجمون لحماية أنظمتكم</p>
                </div>
                <div class="feature-card">
                    <h3>امتثال للمعايير</h3>
                    <p>ضمان امتثال أنظمتكم للمعايير الأمنية المحلية والدولية</p>
                </div>
                <div class="feature-card">
                    <h3>حماية السمعة</h3>
                    <p>تجنب الحوادث الأمنية التي قد تضر بسمعة شركتكم وثقة العملاء</p>
                </div>
                <div class="feature-card">
                    <h3>توفير التكاليف</h3>
                    <p>تجنب التكاليف الباهظة لمعالجة الحوادث الأمنية والخسائر المالية</p>
                </div>
                <div class="feature-card">
                    <h3>تحسين الأمان</h3>
                    <p>تعزيز الوضع الأمني العام لمؤسستكم بشكل مستمر ومنهجي</p>
                </div>
                <div class="feature-card">
                    <h3>خبرة متخصصة</h3>
                    <p>الاستفادة من خبرة فريق متخصص في أمن المعلومات والاختبارات الأمنية</p>
                </div>
            </div>
        </section>
        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تريدون تقييم أمان أنظمتكم؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">احجزوا فحصاً أمنياً شاملاً اليوم لحماية أعمالكم من التهديدات الإلكترونية</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                showPageTransition('Switching to English...', 'جاري التبديل إلى الإنجليزية...', () => {
                    window.location.href = 'security-assessment-en.html';
                });
            }
        }

        // إظهار شاشة الانتقال
        function showPageTransition(mainText, subText, callback) {
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            transition.innerHTML = `
                <div class="transition-content">
                    <div class="transition-logo">
                        <img src="image/logo-8.png" alt="MCT Logo" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="transition-text">${mainText}</div>
                    <div class="transition-subtext">${subText}</div>
                </div>
            `;
            document.body.appendChild(transition);
            setTimeout(() => transition.classList.add('active'), 50);
            setTimeout(() => callback(), 1200);
        }

        window.addEventListener('load', createParticles);


    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
