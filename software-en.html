<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Software Development - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">MCT - Software Development</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index-en.html" class="logo-section">
                <div class="company-name">MCT</div>
                <div class="logo">
                    <img src="image/logo-8.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking-en.html">Network Solutions</a></li>
                    <li><a href="software-en.html" class="active">Software Development</a></li>
                    <li><a href="cybersecurity-en.html">Information Security</a></li>
                    <li><a href="security-assessment-en.html">Security Assessment</a></li>
                    <li><a href="maintenance-en.html">Support & Maintenance</a></li>

                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('ar')">AR</button>
                <a href="index-en.html" class="back-btn">Back to Home</a>
            </div>
        </div>
    </header>

    <main class="main-content">
        <section class="service-hero">
            <div class="service-icon"><i class="fas fa-code"></i></div>
            <h1 class="service-title">Software Development</h1>
            <p class="service-subtitle">We develop innovative and custom software solutions that meet your unique needs. From simple applications to complex systems, we are your partner in digital transformation.</p>
        </section>
        <section class="content-section fade-in-element delay-2">
            <h2 class="section-title">Our Software Services</h2>
            <div class="services-with-animation">
                <div class="services-text">
                    <ul class="services-list">
                        <li><strong>Custom Application Development:</strong> Programming applications designed specifically for your requirements</li>
                        <li><strong>Web Applications:</strong> Interactive and responsive websites and web applications</li>
                        <li><strong>Mobile Applications:</strong> Professional iOS and Android applications</li>
                        <li><strong>Database Management Systems:</strong> Advanced and secure database solutions</li>
                        <li><strong>Content Management Systems:</strong> Custom and user-friendly CMS platforms</li>
                        <li><strong>System Integration:</strong> Connecting different systems and data exchange</li>
                        <li><strong>Maintenance and Support:</strong> Continuous support and regular software updates</li>
                    </ul>
                </div>
                <div class="services-animation">
                    <div class="dashboard-container">
                        <div class="rotating-images">
                            <div class="image-orbit">
                                <!-- Top -->
                                <img src="image/file_11634597.png" alt="File Icon" class="rotating-image" style="top: -5%; left: 50%; transform: translate(-50%, -50%);">
                                <!-- Top Right -->
                                <img src="image/document_11665380.png" alt="Document Icon" class="rotating-image" style="top: 20%; right: 0; transform: translate(50%, -50%);">
                                <!-- Bottom Right -->
                                <img src="image/c-sharp_6132221.png" alt="C# Icon" class="rotating-image" style="bottom: 20%; right: 0; transform: translate(50%, 50%);">
                                <!-- Bottom -->
                                <img src="image/web-coding_11513813.png" alt="Web Coding Icon" class="rotating-image" style="bottom: -3%; left: 50%; transform: translate(-50%, 50%);">
                                <!-- Bottom Left -->
                                <img src="image/code_4997543.png" alt="Code Icon" class="rotating-image" style="bottom: 20%; left: 0; transform: translate(-50%, 50%);">
                                <!-- Top Left -->
                                <img src="image/java_3291669.png" alt="Java Icon" class="rotating-image" style="top: 20%; left: 0; transform: translate(-50%, -50%);">
                            </div>
                        </div>
                        <!-- Main Laptop -->
                        <div class="laptop">
                            <div class="laptop-screen">
                                <div class="screen-content">
                                    <div class="window-bar">
                                        <div class="window-buttons">
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                            <div class="window-button"></div>
                                        </div>
                                    </div>
                                    <div class="content-area">
                                        <div class="left-panel">
                                            <div class="blue-section"></div>
                                            <div class="green-section"></div>
                                        </div>
                                        <div class="right-panel">
                                            <div class="data-lines">
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                                <div class="line"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Connection Lines -->
                        <div class="connection-lines">
                            <div class="connection-line line-1"></div>
                            <div class="connection-line line-2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="content-section">
            <h2 class="section-title">Why Choose Our Services?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Modern Technologies</h3>
                    <p>We use the latest programming languages and technical frameworks to ensure outstanding performance and a guaranteed future</p>
                </div>
                <div class="feature-card">
                    <h3>Custom Design</h3>
                    <p>Each project is designed specifically to suit your needs and the nature of your work with precision</p>
                </div>
                <div class="feature-card">
                    <h3>Easy Interfaces</h3>
                    <p>Design intuitive and easy-to-use user interfaces that improve user experience</p>
                </div>
                <div class="feature-card">
                    <h3>High Security</h3>
                    <p>Apply the highest security standards to protect your data and customer information</p>
                </div>
                <div class="feature-card">
                    <h3>Scalability</h3>
                    <p>Flexible software that can be developed and expanded with the growth of your business</p>
                </div>
                <div class="feature-card">
                    <h3>Comprehensive Support</h3>
                    <p>Continuous technical support and comprehensive training for your team on using the systems</p>
                </div>
            </div>
        </section>

        <section class="contact-cta">
            <h2 style="color: #008B80; margin-bottom: 20px;">Do you have an idea for an application or system?</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">Contact us to turn your ideas into an outstanding digital reality</p>
            <a href="index-en.html#contact" class="cta-button">Contact Us</a>
            <a href="tel:+966123456789" class="cta-button">Call Us</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'ar') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'software.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

        // إعداد الشكل المتحرك في صفحة البرمجيات
        document.addEventListener('DOMContentLoaded', function() {
            const dashboardContainer = document.querySelector('.dashboard-container');
            const imageOrbit = document.querySelector('.image-orbit');

            if (!dashboardContainer || !imageOrbit) return;

            let currentRotation = 0;
            let isRotating = false;
            let animationFrame;
            let lastTimestamp = 0;
            const rotationSpeed = 0.5;

            function animate(timestamp) {
                if (!lastTimestamp) lastTimestamp = timestamp;

                if (isRotating) {
                    currentRotation += rotationSpeed;
                    if (currentRotation >= 360) {
                        currentRotation = currentRotation % 360;
                    }
                    imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
                    lastTimestamp = timestamp;
                    animationFrame = requestAnimationFrame(animate);
                }
            }

            dashboardContainer.addEventListener('mouseenter', function() {
                if (!isRotating) {
                    isRotating = true;
                    lastTimestamp = 0;
                    animationFrame = requestAnimationFrame(animate);
                }
            });

            dashboardContainer.addEventListener('mouseleave', function() {
                isRotating = false;
                cancelAnimationFrame(animationFrame);
                imageOrbit.style.transition = 'none';
            });
        });

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
    <script src="js/translation.js"></script>
</body>
</html>
